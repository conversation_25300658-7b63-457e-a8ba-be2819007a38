import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// 测试页面专用配置
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  root: '.',
  build: {
    rollupOptions: {
      input: {
        'index-test': './index-test.html',
        test: './test.html',
        'simple-test': './simple-test.html',
        'advanced-test': './advanced-test.html',
        'autocomplete-test': './autocomplete-test.html'
      }
    }
  },
  server: {
    port: 5174,
    open: '/index-test.html'
  }
})
