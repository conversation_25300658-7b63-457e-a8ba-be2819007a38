// 华为地图组件测试
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import HuaweiMap from '../components/HuaweiMap.vue';

// Mock 华为地图API
const mockHWMapJsSDK = {
  HWMap: vi.fn().mockImplementation(() => ({
    setCenter: vi.fn(),
    setZoom: vi.fn(),
    getCenter: vi.fn().mockReturnValue({ lat: 39.9042, lng: 116.4074 }),
    getZoom: vi.fn().mockReturnValue(10),
    setOpacity: vi.fn(),
    resize: vi.fn(),
    on: vi.fn(),
    onCenterChanged: vi.fn(),
    onZoomChanged: vi.fn(),
    onHeadingChanged: vi.fn()
  })),
  HWMarker: vi.fn().mockImplementation(() => ({
    setPosition: vi.fn(),
    setMap: vi.fn(),
    setIcon: vi.fn(),
    setLabel: vi.fn(),
    setDraggable: vi.fn(),
    setAnimation: vi.fn(),
    setZIndex: vi.fn(),
    getPosition: vi.fn(),
    addListener: vi.fn()
  })),
  HWInfoWindow: vi.fn().mockImplementation(() => ({
    setPosition: vi.fn(),
    setContent: vi.fn(),
    open: vi.fn(),
    close: vi.fn(),
    addListener: vi.fn()
  }))
};

// Mock window.HWMapJsSDK
Object.defineProperty(window, 'HWMapJsSDK', {
  value: mockHWMapJsSDK,
  writable: true
});

// Mock map loader
vi.mock('../utils/map-loader', () => ({
  loadHuaweiMapAPI: vi.fn().mockResolvedValue(undefined),
  isHuaweiMapAPILoaded: vi.fn().mockReturnValue(true)
}));

describe('HuaweiMap Component', () => {
  let wrapper: any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该正确渲染地图容器', () => {
    wrapper = mount(HuaweiMap, {
      props: {
        center: { lat: 39.9042, lng: 116.4074 },
        zoom: 10,
        width: '100%',
        height: '400px'
      }
    });

    expect(wrapper.find('.huawei-map-container').exists()).toBe(true);
    expect(wrapper.find('.huawei-map-container').attributes('style')).toContain('width: 100%');
    expect(wrapper.find('.huawei-map-container').attributes('style')).toContain('height: 400px');
  });

  it('应该使用正确的默认props', () => {
    wrapper = mount(HuaweiMap);

    expect(wrapper.props('width')).toBe('100%');
    expect(wrapper.props('height')).toBe('400px');
    expect(wrapper.props('zoom')).toBe(8);
    expect(wrapper.props('minZoom')).toBe(2);
    expect(wrapper.props('maxZoom')).toBe(22);
    expect(wrapper.props('zoomControl')).toBe(true);
  });

  it('应该在地图准备就绪时触发事件', async () => {
    wrapper = mount(HuaweiMap, {
      props: {
        center: { lat: 39.9042, lng: 116.4074 },
        zoom: 10
      }
    });

    // 等待组件挂载和地图初始化
    await wrapper.vm.$nextTick();

    // 模拟地图初始化完成
    await wrapper.vm.initMap();

    expect(wrapper.emitted('map-ready')).toBeTruthy();
  });

  it('应该正确处理props变化', async () => {
    wrapper = mount(HuaweiMap, {
      props: {
        center: { lat: 39.9042, lng: 116.4074 },
        zoom: 10
      }
    });

    // 等待初始化
    await wrapper.vm.$nextTick();
    await wrapper.vm.initMap();

    // 更改center prop
    await wrapper.setProps({
      center: { lat: 31.2304, lng: 121.4737 }
    });

    // 验证地图实例的setCenter方法被调用
    expect(wrapper.vm.map?.setCenter).toHaveBeenCalledWith({ lat: 31.2304, lng: 121.4737 });
  });

  it('应该正确处理缩放级别变化', async () => {
    wrapper = mount(HuaweiMap, {
      props: {
        center: { lat: 39.9042, lng: 116.4074 },
        zoom: 10
      }
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.initMap();

    // 更改zoom prop
    await wrapper.setProps({ zoom: 15 });

    expect(wrapper.vm.map?.setZoom).toHaveBeenCalledWith(15);
  });

  it('应该正确处理透明度变化', async () => {
    wrapper = mount(HuaweiMap, {
      props: {
        center: { lat: 39.9042, lng: 116.4074 },
        zoom: 10,
        opacity: 0.8
      }
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.initMap();

    // 更改opacity prop
    await wrapper.setProps({ opacity: 0.5 });

    expect(wrapper.vm.map?.setOpacity).toHaveBeenCalledWith(0.5);
  });

  it('应该显示加载状态', () => {
    wrapper = mount(HuaweiMap);

    expect(wrapper.find('.map-loading').exists()).toBe(true);
    expect(wrapper.find('.loading-spinner').exists()).toBe(true);
  });

  it('应该显示错误状态', async () => {
    // Mock loadHuaweiMapAPI to reject
    const { loadHuaweiMapAPI } = await import('../utils/map-loader');
    vi.mocked(loadHuaweiMapAPI).mockRejectedValueOnce(new Error('API加载失败'));

    wrapper = mount(HuaweiMap);

    // 等待组件挂载完成
    await wrapper.vm.$nextTick();

    // 等待initMap被调用并处理错误
    await new Promise(resolve => setTimeout(resolve, 100));
    await wrapper.vm.$nextTick();

    expect(wrapper.find('.map-error').exists()).toBe(true);
    expect(wrapper.find('.retry-button').exists()).toBe(true);
  });

  it('应该正确暴露地图实例和方法', async () => {
    wrapper = mount(HuaweiMap, {
      props: {
        center: { lat: 39.9042, lng: 116.4074 },
        zoom: 10
      }
    });

    await wrapper.vm.$nextTick();
    // 等待地图初始化完成

    // 验证暴露的方法存在
    expect(typeof wrapper.vm.getMap).toBe('function');
    expect(typeof wrapper.vm.reload).toBe('function');
    expect(typeof wrapper.vm.addMarker).toBe('function');
    expect(typeof wrapper.vm.removeMarker).toBe('function');
    expect(typeof wrapper.vm.clearMarkers).toBe('function');
    expect(typeof wrapper.vm.addInfoWindow).toBe('function');
    expect(typeof wrapper.vm.removeInfoWindow).toBe('function');
  });
});

describe('Map Configuration', () => {
  it('应该使用正确的地图配置', async () => {
    const wrapper = mount(HuaweiMap, {
      props: {
        center: { lat: 39.9042, lng: 116.4074 },
        zoom: 12,
        language: 'en',
        sourceType: 'vector',
        mapType: 'TERRAIN',
        minZoom: 5,
        maxZoom: 18,
        copyrightControl: true,
        locationControl: true,
        navigationControl: true,
        rotateControl: true,
        scaleControl: true,
        zoomControl: false,
        zoomSlider: true,
        logoPosition: 'TOP_RIGHT',
        presetStyleId: 'night'
      }
    });

    await wrapper.vm.$nextTick();
    // 等待地图初始化完成

    // 验证HWMap构造函数被调用
    expect(mockHWMapJsSDK.HWMap).toHaveBeenCalled();

    // 验证传入的配置包含我们期望的值
    const callArgs = mockHWMapJsSDK.HWMap.mock.calls[0];
    const config = callArgs[1];

    expect(config.center).toEqual({ lat: 39.9042, lng: 116.4074 });
    expect(config.zoom).toBe(12);
    expect(config.language).toBe('en');
    expect(config.sourceType).toBe('vector');
    expect(config.mapType).toBe('TERRAIN');
    expect(config.minZoom).toBe(5);
    expect(config.maxZoom).toBe(18);
    expect(config.copyrightControl).toBe(true);
    expect(config.locationControl).toBe(true);
    expect(config.navigationControl).toBe(true);
    expect(config.rotateControl).toBe(true);
    expect(config.scaleControl).toBe(true);
    expect(config.zoomControl).toBe(false);
    expect(config.zoomSlider).toBe(true);
    expect(config.logoPosition).toBe('TOP_RIGHT');
    expect(config.presetStyleId).toBe('night');
  });
});

describe('Marker Management', () => {
  let wrapper: any;

  beforeEach(async () => {
    wrapper = mount(HuaweiMap, {
      props: {
        center: { lat: 39.9042, lng: 116.4074 },
        zoom: 10
      }
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.initMap();
  });

  it('应该能够添加标记', () => {
    const markerId = wrapper.vm.addMarker({
      position: { lat: 39.9042, lng: 116.4074 },
      title: '测试标记',
      content: '这是一个测试标记'
    });

    expect(typeof markerId).toBe('string');
    expect(wrapper.vm.markerCount).toBe(1);
    expect(mockHWMapJsSDK.HWMarker).toHaveBeenCalled();
  });

  it('应该能够删除标记', () => {
    const markerId = wrapper.vm.addMarker({
      position: { lat: 39.9042, lng: 116.4074 },
      title: '测试标记'
    });

    const removed = wrapper.vm.removeMarker(markerId);

    expect(removed).toBe(true);
    expect(wrapper.vm.markerCount).toBe(0);
  });

  it('应该能够清空所有标记', () => {
    // 添加多个标记
    wrapper.vm.addMarker({ position: { lat: 39.9042, lng: 116.4074 } });
    wrapper.vm.addMarker({ position: { lat: 39.9142, lng: 116.4174 } });
    wrapper.vm.addMarker({ position: { lat: 39.8942, lng: 116.3974 } });

    expect(wrapper.vm.markerCount).toBe(3);

    const clearedCount = wrapper.vm.clearMarkers();

    expect(clearedCount).toBe(3);
    expect(wrapper.vm.markerCount).toBe(0);
  });
});

describe('InfoWindow Management', () => {
  let wrapper: any;

  beforeEach(async () => {
    wrapper = mount(HuaweiMap, {
      props: {
        center: { lat: 39.9042, lng: 116.4074 },
        zoom: 10
      }
    });

    await wrapper.vm.$nextTick();
    await wrapper.vm.initMap();
  });

  it('应该能够添加信息窗', () => {
    const infoWindowId = wrapper.vm.addInfoWindow({
      position: { lat: 39.9042, lng: 116.4074 },
      content: '测试信息窗内容'
    });

    expect(typeof infoWindowId).toBe('string');
    expect(wrapper.vm.infoWindowCount).toBe(1);
    expect(mockHWMapJsSDK.HWInfoWindow).toHaveBeenCalled();
  });

  it('应该能够打开和关闭信息窗', () => {
    const infoWindowId = wrapper.vm.addInfoWindow({
      position: { lat: 39.9042, lng: 116.4074 },
      content: '测试信息窗内容'
    });

    const opened = wrapper.vm.openInfoWindow(infoWindowId);
    expect(opened).toBe(true);

    const closed = wrapper.vm.closeInfoWindow(infoWindowId);
    expect(closed).toBe(true);
  });

  it('应该能够清空所有信息窗', () => {
    // 添加多个信息窗
    wrapper.vm.addInfoWindow({ position: { lat: 39.9042, lng: 116.4074 }, content: '内容1' });
    wrapper.vm.addInfoWindow({ position: { lat: 39.9142, lng: 116.4174 }, content: '内容2' });

    expect(wrapper.vm.infoWindowCount).toBe(2);

    const clearedCount = wrapper.vm.clearInfoWindows();

    expect(clearedCount).toBe(2);
    expect(wrapper.vm.infoWindowCount).toBe(0);
  });
});
