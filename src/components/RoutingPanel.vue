<template>
  <div class="routing-panel">
    <div class="routing-header">
      <h3>路径规划</h3>
      <button
        class="toggle-btn"
        @click="togglePanel"
        :class="{ active: isExpanded }"
      >
        {{ isExpanded ? '收起' : '展开' }}
      </button>
    </div>

    <div v-show="isExpanded" class="routing-content">
      <!-- 路径类型选择 -->
      <div class="route-type-selector">
        <div class="type-tabs">
          <button
            v-for="type in routeTypes"
            :key="type.value"
            class="type-tab"
            :class="{ active: selectedRouteType === type.value }"
            @click="selectedRouteType = type.value as RouteType"
          >
            {{ type.icon }} {{ type.label }}
          </button>
        </div>
      </div>

      <!-- 起终点输入 -->
      <div class="waypoints-input">
        <div class="waypoint-group">
          <label class="waypoint-label">起点:</label>
          <HWAutocomplete
            v-model="originInput"
            placeholder="输入起点地址或点击地图选择"
            input-class="waypoint-input"
            :location="mapCenter"
            :radius="10000"
            poi-type="GEOCODE"
            @site-selected="onOriginSelected"
            @enter="geocodeOrigin"
          />
          <button
            class="location-btn"
            @click="useCurrentLocation('origin')"
            title="使用当前位置"
          >
            📍
          </button>
        </div>

        <div class="waypoint-group">
          <label class="waypoint-label">终点:</label>
          <HWAutocomplete
            v-model="destinationInput"
            placeholder="输入终点地址或点击地图选择"
            input-class="waypoint-input"
            :location="mapCenter"
            :radius="10000"
            poi-type="GEOCODE"
            @site-selected="onDestinationSelected"
            @enter="geocodeDestination"
          />
          <button
            class="location-btn"
            @click="selectFromMap('destination')"
            title="从地图选择"
          >
            🗺️
          </button>
        </div>

        <!-- 途经点 -->
        <div v-if="waypoints.length > 0" class="waypoints-list">
          <div class="waypoints-header">
            <span>途经点:</span>
            <button class="clear-waypoints-btn" @click="clearWaypoints">清除</button>
          </div>
          <div
            v-for="(waypoint, index) in waypoints"
            :key="index"
            class="waypoint-item"
          >
            <div class="waypoint-input-group">
              <span class="waypoint-index">{{ index + 1 }}.</span>
              <HWAutocomplete
                v-model="waypoint.address"
                :placeholder="`输入途经点${index + 1}地址`"
                input-class="waypoint-input-small"
                :location="mapCenter"
                :radius="10000"
                poi-type="GEOCODE"
                @site-selected="(site, result) => onWaypointSelected(index, site, result)"
                @enter="() => geocodeWaypoint(index)"
              />
              <button
                class="remove-waypoint-btn"
                @click="removeWaypoint(index)"
                title="删除途经点"
              >
                ×
              </button>
            </div>
          </div>
        </div>

        <button
          class="add-waypoint-btn"
          @click="addWaypoint"
          :disabled="waypoints.length >= 5"
        >
          + 添加途经点
        </button>
      </div>

      <!-- 路径选项 -->
      <div v-if="selectedRouteType === 'driving'" class="route-options">
        <h4>驾车选项</h4>
        <div class="option-group">
          <label>路径策略:</label>
          <select v-model="drivingOptions.strategy">
            <option value="fastest">最快路径</option>
            <option value="shortest">最短路径</option>
            <option value="avoid_traffic">避开拥堵</option>
            <option value="avoid_highway">避开高速</option>
          </select>
        </div>
        <div class="option-group">
          <label>车辆类型:</label>
          <select v-model="drivingOptions.vehicleType">
            <option value="car">小汽车</option>
            <option value="truck">货车</option>
            <option value="motorcycle">摩托车</option>
          </select>
        </div>
      </div>

      <div v-if="selectedRouteType === 'transit'" class="route-options">
        <h4>公交选项</h4>
        <div class="option-group">
          <label>出行时间:</label>
          <input
            v-model="transitTime"
            type="datetime-local"
            class="time-input"
          />
        </div>
        <div class="option-group">
          <label>交通方式:</label>
          <div class="transit-modes">
            <label class="mode-checkbox">
              <input
                v-model="transitOptions.modes"
                type="checkbox"
                value="bus"
              />
              公交
            </label>
            <label class="mode-checkbox">
              <input
                v-model="transitOptions.modes"
                type="checkbox"
                value="subway"
              />
              地铁
            </label>
            <label class="mode-checkbox">
              <input
                v-model="transitOptions.modes"
                type="checkbox"
                value="train"
              />
              火车
            </label>
          </div>
        </div>
      </div>

      <!-- 规划按钮 -->
      <div class="planning-actions">
        <button
          class="plan-btn"
          @click="planRoute"
          :disabled="isPlanning || !canPlan"
        >
          {{ isPlanning ? '规划中...' : '开始规划' }}
        </button>
        <button
          class="clear-btn"
          @click="clearRoute"
          :disabled="!hasRoute"
        >
          清除路径
        </button>
      </div>

      <!-- 错误信息 -->
      <div v-if="hasError" class="error-message">
        {{ routingError }}
      </div>

      <!-- 路径结果 -->
      <div v-if="hasRoute" class="route-result">
        <div class="result-header">
          <h4>路径信息</h4>
          <button
            class="display-btn"
            @click="toggleRouteDisplay"
          >
            {{ isRouteDisplayed ? '隐藏路径' : '显示路径' }}
          </button>
        </div>

        <div class="result-summary">
          <div class="summary-item">
            <span class="summary-label">总距离:</span>
            <span class="summary-value">{{ formatDistance(currentRoute?.totalDistance || 0) }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">预计时间:</span>
            <span class="summary-value">{{ formatDuration(currentRoute?.totalDuration || 0) }}</span>
          </div>
          <div v-if="currentRoute?.fare" class="summary-item">
            <span class="summary-label">费用:</span>
            <span class="summary-value">{{ currentRoute.fare.value }} {{ currentRoute.fare.currency }}</span>
          </div>
        </div>

        <!-- 路径详情 -->
        <div class="route-details">
          <div class="details-header">
            <span>路径详情</span>
            <button
              class="toggle-details-btn"
              @click="showDetails = !showDetails"
            >
              {{ showDetails ? '收起' : '展开' }}
            </button>
          </div>

          <div v-show="showDetails" class="details-content">
            <div
              v-for="(leg, legIndex) in currentRoute?.legs || []"
              :key="legIndex"
              class="route-leg"
            >
              <div class="leg-header">
                <span>段落 {{ legIndex + 1 }}</span>
                <span class="leg-distance">{{ formatDistance(leg.distance) }}</span>
              </div>

              <div class="route-steps">
                <div
                  v-for="(step, stepIndex) in leg.steps"
                  :key="stepIndex"
                  class="route-step"
                >
                  <div class="step-instruction" v-html="step.instruction"></div>
                  <div class="step-meta">
                    <span class="step-distance">{{ formatDistance(step.distance) }}</span>
                    <span class="step-duration">{{ formatDuration(step.duration) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 路径历史 -->
      <div v-if="routeHistory.length > 0" class="route-history">
        <div class="history-header">
          <span>历史路径</span>
          <button class="clear-history-btn" @click="clearRouteHistory">清除</button>
        </div>
        <div class="history-list">
          <div
            v-for="(route, index) in routeHistory"
            :key="route.id"
            class="history-item"
            @click="selectHistoryRoute(route)"
          >
            <div class="history-info">
              <div class="history-summary">{{ route.summary }}</div>
              <div class="history-meta">
                {{ formatDistance(route.totalDistance) }} · {{ formatDuration(route.totalDuration) }}
              </div>
            </div>
            <div class="history-type">{{ getRouteTypeText(route.type) }}</div>
          </div>
        </div>
      </div>

      <!-- 路径统计 -->
      <div class="routing-stats">
        <div class="stat-item">
          <span class="stat-label">总规划次数:</span>
          <span class="stat-value">{{ routingStats.totalRoutes }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">成功率:</span>
          <span class="stat-value">{{ successRate.toFixed(1) }}%</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">平均距离:</span>
          <span class="stat-value">{{ formatDistance(averageDistance) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useRouting } from '../composables/useRouting'
import { useSearch } from '../composables/useSearch'
import HWAutocomplete from './HWAutocomplete.vue'
import type { LatLng, RouteType, DrivingOptions, TransitOptions } from '../types/routing'
import type { SearchResult } from '../types/search'

// Props
interface Props {
  map?: any
  mapCenter?: LatLng
}

const props = withDefaults(defineProps<Props>(), {
  mapCenter: () => ({ lat: 39.9042, lng: 116.4074 })
})

// Emits
const emit = defineEmits<{
  'route-planned': [route: any]
  'route-displayed': [route: any]
  'route-cleared': []
  'waypoint-selected': [type: string]
}>()

// 使用路径规划功能
const {
  isPlanning,
  routingError,
  isRouteDisplayed,
  currentRoute,
  routeHistory,
  routingStats,
  hasRoute,
  hasError,
  successRate,
  averageDistance,
  planDrivingRoute,
  planWalkingRoute,
  planBicyclingRoute,
  planTransitRoute,
  planMultiPointRoute,
  displayRoute,
  clearRoute,
  clearHistory,
  formatDistance,
  formatDuration,
  setMap
} = useRouting(props.map)

// 使用搜索功能进行地理编码
const { geocode } = useSearch()

// 组件状态
const isExpanded = ref(true)
const showDetails = ref(false)
const selectedRouteType = ref<RouteType>('driving')

// 路径类型选项
const routeTypes = [
  { value: 'driving', label: '驾车', icon: '🚗' },
  { value: 'walking', label: '步行', icon: '🚶' },
  { value: 'bicycling', label: '骑行', icon: '🚴' },
  { value: 'transit', label: '公交', icon: '🚌' }
]

// 起终点输入
const originInput = ref('')
const destinationInput = ref('')
const origin = ref<LatLng | null>(null)
const destination = ref<LatLng | null>(null)

// 途经点
const waypoints = ref<Array<{ address: string; location: LatLng }>>([])

// 路径选项
const drivingOptions = reactive<DrivingOptions>({
  strategy: 'fastest',
  vehicleType: 'car'
})

const transitOptions = reactive<TransitOptions>({
  modes: ['bus', 'subway'],
  includeWalking: true
})

const transitTime = ref('')

// 计算属性
const canPlan = computed(() => {
  return origin.value && destination.value
})

// 方法
const togglePanel = () => {
  isExpanded.value = !isExpanded.value
}

// 自动补全选择事件处理
const onOriginSelected = (site: any, result: SearchResult) => {
  console.log('选择起点:', result)
  origin.value = result.location
  originInput.value = result.name || result.address
}

const onDestinationSelected = (site: any, result: SearchResult) => {
  console.log('选择终点:', result)
  destination.value = result.location
  destinationInput.value = result.name || result.address
}

const geocodeOrigin = async () => {
  if (!originInput.value.trim()) return

  try {
    const result = await geocode(originInput.value)
    origin.value = result.location
  } catch (error) {
    console.error('起点地理编码失败:', error)
  }
}

const geocodeDestination = async () => {
  if (!destinationInput.value.trim()) return

  try {
    const result = await geocode(destinationInput.value)
    destination.value = result.location
  } catch (error) {
    console.error('终点地理编码失败:', error)
  }
}

const useCurrentLocation = (type: 'origin' | 'destination') => {
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const location = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        }

        if (type === 'origin') {
          origin.value = location
          originInput.value = '当前位置'
        } else {
          destination.value = location
          destinationInput.value = '当前位置'
        }
      },
      (error) => {
        console.error('获取当前位置失败:', error)
      }
    )
  }
}

const selectFromMap = (type: 'origin' | 'destination') => {
  emit('waypoint-selected', type)
}

// 途经点事件处理
const onWaypointSelected = (index: number, site: any, result: SearchResult) => {
  console.log(`选择途经点${index + 1}:`, result)
  if (waypoints.value[index]) {
    waypoints.value[index].location = result.location
    waypoints.value[index].address = result.name || result.address
  }
}

const geocodeWaypoint = async (index: number) => {
  const waypoint = waypoints.value[index]
  if (!waypoint || !waypoint.address.trim()) return

  try {
    const result = await geocode(waypoint.address)
    waypoint.location = result.location
  } catch (error) {
    console.error(`途经点${index + 1}地理编码失败:`, error)
  }
}

const addWaypoint = () => {
  if (waypoints.value.length < 5) {
    waypoints.value.push({
      address: '',
      location: { lat: 0, lng: 0 }
    })
  }
}

const removeWaypoint = (index: number) => {
  waypoints.value.splice(index, 1)
}

const clearWaypoints = () => {
  waypoints.value = []
}

const planRoute = async () => {
  if (!origin.value || !destination.value) {
    console.warn('起点或终点未设置')
    return
  }

  // 检查华为地图SDK
  if (!window.HWMapJsSDK) {
    console.error('华为地图SDK未加载')
    return
  }

  // 检查地图实例
  if (!props.map) {
    console.error('地图实例未设置')
    return
  }

  console.log('开始路径规划:', {
    origin: origin.value,
    destination: destination.value,
    type: selectedRouteType.value,
    waypoints: waypoints.value,
    map: !!props.map,
    sdk: !!window.HWMapJsSDK
  })

  isPlanning.value = true

  try {
    let route

    switch (selectedRouteType.value) {
      case 'driving':
        if (waypoints.value.length > 0) {
          const allWaypoints = [
            origin.value,
            ...waypoints.value.map(wp => wp.location),
            destination.value
          ]
          console.log('规划多点驾车路径:', allWaypoints)
          route = await planMultiPointRoute(allWaypoints, {
            type: 'driving',
            drivingOptions: drivingOptions
          })
        } else {
          console.log('规划驾车路径:', origin.value, destination.value)
          route = await planDrivingRoute(origin.value, destination.value, drivingOptions)
        }
        break
      case 'walking':
        console.log('规划步行路径:', origin.value, destination.value)
        route = await planWalkingRoute(origin.value, destination.value)
        break
      case 'bicycling':
        console.log('规划骑行路径:', origin.value, destination.value)
        route = await planBicyclingRoute(origin.value, destination.value)
        break
      case 'transit':
        const options = { ...transitOptions }
        if (transitTime.value) {
          options.departureTime = new Date(transitTime.value)
        }
        console.log('规划公交路径:', origin.value, destination.value, options)
        route = await planTransitRoute(origin.value, destination.value, options)
        break
    }

    if (route) {
      console.log('路径规划成功:', route)
      currentRoute.value = route
      hasRoute.value = true

      // 自动显示路径
      displayRoute(route)

      emit('route-planned', route)
    } else {
      console.warn('路径规划返回空结果')
    }
  } catch (error) {
    console.error('路径规划失败:', error)
    // 可以在这里添加用户友好的错误提示
  } finally {
    isPlanning.value = false
  }
}

const toggleRouteDisplay = () => {
  if (isRouteDisplayed.value) {
    clearRoute()
  } else if (currentRoute.value) {
    displayRoute()
    emit('route-displayed', currentRoute.value)
  }
}

const clearRouteHistory = () => {
  clearHistory()
}

const selectHistoryRoute = (route: any) => {
  displayRoute(route)
  emit('route-displayed', route)
}

const getRouteTypeText = (type: RouteType) => {
  const typeMap = {
    driving: '驾车',
    walking: '步行',
    bicycling: '骑行',
    transit: '公交'
  }
  return typeMap[type] || type
}

// 监听地图变化
watch(() => props.map, (newMap) => {
  if (newMap) {
    console.log('更新路径规划服务的地图实例')
    setMap(newMap)
  }
})

// 监听公交时间变化
watch(transitTime, (newTime) => {
  if (newTime) {
    transitOptions.departureTime = new Date(newTime)
  }
})
</script>

<style scoped>
.routing-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: visible;
  position: relative;
  z-index: 100;
}

.routing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.routing-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.toggle-btn {
  padding: 4px 8px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 12px;
}

.toggle-btn:hover {
  background: #f0f0f0;
}

.toggle-btn.active {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.routing-content {
  padding: 16px;
}

.route-type-selector {
  margin-bottom: 16px;
}

.type-tabs {
  display: flex;
  gap: 4px;
  background: #f5f5f5;
  border-radius: 6px;
  padding: 4px;
}

.type-tab {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  background: transparent;
  cursor: pointer;
  font-size: 12px;
  text-align: center;
}

.type-tab:hover {
  background: rgba(255, 255, 255, 0.5);
}

.type-tab.active {
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.waypoints-input {
  margin-bottom: 16px;
  position: relative;
  z-index: 200;
}

.waypoint-group {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 12px;
}

.waypoint-label {
  min-width: 40px;
  font-size: 14px;
  font-weight: 500;
  margin-top: 8px;
}

.waypoint-group :deep(.hw-autocomplete) {
  flex: 1;
}

.waypoint-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 14px;
}

.waypoint-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.location-btn {
  padding: 8px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  height: 36px;
  min-width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-btn:hover {
  background: #f0f0f0;
}

.waypoints-list {
  margin: 12px 0;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 4px;
}

.waypoints-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}

.clear-waypoints-btn {
  padding: 2px 6px;
  background: none;
  border: 1px solid #d0d0d0;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.clear-waypoints-btn:hover {
  background: #f0f0f0;
}

.waypoint-item {
  margin-bottom: 8px;
}

.waypoint-input-group {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.waypoint-index {
  min-width: 20px;
  font-size: 12px;
  font-weight: 500;
  color: #333;
  margin-top: 8px;
}

.waypoint-input-group :deep(.hw-autocomplete) {
  flex: 1;
}

.waypoint-input-small {
  padding: 6px 8px;
  font-size: 12px;
}

.waypoint-text {
  flex: 1;
  font-size: 12px;
}

.remove-waypoint-btn {
  padding: 6px 8px;
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  height: 32px;
  min-width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-waypoint-btn:hover {
  background: #ff7875;
}

.add-waypoint-btn {
  padding: 6px 12px;
  background: #52c41a;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.add-waypoint-btn:hover:not(:disabled) {
  background: #73d13d;
}

.add-waypoint-btn:disabled {
  background: #d0d0d0;
  cursor: not-allowed;
}

.route-options {
  margin-bottom: 16px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 4px;
}

.route-options h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
}

.option-group {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.option-group label {
  min-width: 80px;
  font-size: 12px;
  color: #666;
}

.option-group select {
  flex: 1;
  padding: 4px 6px;
  border: 1px solid #d0d0d0;
  border-radius: 3px;
  font-size: 12px;
}

.time-input {
  flex: 1;
  padding: 4px 6px;
  border: 1px solid #d0d0d0;
  border-radius: 3px;
  font-size: 12px;
}

.transit-modes {
  display: flex;
  gap: 12px;
}

.mode-checkbox {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  cursor: pointer;
}

.planning-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.plan-btn {
  flex: 1;
  padding: 10px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.plan-btn:hover:not(:disabled) {
  background: #40a9ff;
}

.plan-btn:disabled {
  background: #d0d0d0;
  cursor: not-allowed;
}

.clear-btn {
  padding: 10px 16px;
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.clear-btn:hover:not(:disabled) {
  background: #ff7875;
}

.clear-btn:disabled {
  background: #d0d0d0;
  cursor: not-allowed;
}

.error-message {
  padding: 8px 12px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  color: #ff4d4f;
  font-size: 14px;
  margin-bottom: 12px;
  position: relative;
  z-index: 1000;
}

.route-result {
  margin-bottom: 16px;
  padding: 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.result-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.display-btn {
  padding: 4px 8px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.display-btn:hover {
  background: #40a9ff;
}

.result-summary {
  margin-bottom: 12px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.summary-label {
  color: #333;
  font-weight: 500;
}

.summary-value {
  font-weight: 500;
}

.route-details {
  border-top: 1px solid #d9f7be;
  padding-top: 12px;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.details-header span {
  font-size: 12px;
  font-weight: 500;
}

.toggle-details-btn {
  padding: 2px 6px;
  background: none;
  border: 1px solid #d0d0d0;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
}

.toggle-details-btn:hover {
  background: #f0f0f0;
}

.route-leg {
  margin-bottom: 12px;
  padding: 8px;
  background: white;
  border-radius: 3px;
}

.leg-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 12px;
  font-weight: 500;
}

.leg-distance {
  color: #1890ff;
}

.route-steps {
  max-height: 150px;
  overflow-y: auto;
}

.route-step {
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
}

.route-step:last-child {
  border-bottom: none;
}

.step-instruction {
  font-size: 11px;
  margin-bottom: 2px;
  line-height: 1.4;
}

.step-meta {
  display: flex;
  gap: 8px;
  font-size: 10px;
  color: #666;
}

.route-history {
  margin-bottom: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}

.clear-history-btn {
  padding: 2px 6px;
  background: none;
  border: 1px solid #d0d0d0;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.clear-history-btn:hover {
  background: #f0f0f0;
}

.history-list {
  max-height: 150px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
  margin-bottom: 4px;
  cursor: pointer;
}

.history-item:hover {
  background: #f9f9f9;
  border-color: #d0d0d0;
}

.history-info {
  flex: 1;
}

.history-summary {
  font-size: 12px;
  margin-bottom: 2px;
}

.history-meta {
  font-size: 10px;
  color: #666;
}

.history-type {
  font-size: 10px;
  padding: 2px 6px;
  background: #f0f0f0;
  border-radius: 3px;
}

.routing-stats {
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: 500;
}
</style>
