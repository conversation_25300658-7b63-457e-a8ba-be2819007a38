<template>
  <div class="infowindow-panel">
    <div class="panel-header">
      <h3>信息窗管理</h3>
      <div class="panel-stats">
        <span>总数: {{ infoWindowCount }}</span>
        <span>可见: {{ visibleInfoWindowCount }}</span>
        <span v-if="activeInfoWindow">活动: {{ activeInfoWindow.title || activeInfoWindow.id.slice(-6) }}</span>
      </div>
    </div>

    <div class="panel-actions">
      <button @click="showAddForm = !showAddForm" class="action-btn primary">
        {{ showAddForm ? '取消添加' : '添加信息窗' }}
      </button>
      <button @click="closeAllInfoWindows" class="action-btn secondary" :disabled="visibleInfoWindowCount === 0">
        关闭所有
      </button>
      <button @click="clearAllInfoWindows" class="action-btn danger" :disabled="infoWindowCount === 0">
        清空所有
      </button>
    </div>

    <!-- 添加信息窗表单 -->
    <div v-if="showAddForm" class="add-form">
      <h4>添加新信息窗</h4>
      <div class="form-group">
        <label>标题:</label>
        <input v-model="newInfoWindow.title" type="text" placeholder="信息窗标题" />
      </div>
      <div class="form-group">
        <label>内容:</label>
        <textarea v-model="newInfoWindow.content" placeholder="信息窗内容" rows="3"></textarea>
      </div>
      <div class="form-row">
        <div class="form-group">
          <label>纬度:</label>
          <input v-model.number="newInfoWindow.position.lat" type="number" step="0.000001" />
        </div>
        <div class="form-group">
          <label>经度:</label>
          <input v-model.number="newInfoWindow.position.lng" type="number" step="0.000001" />
        </div>
      </div>
      <div class="form-row">
        <div class="form-group">
          <label>X偏移:</label>
          <input v-model.number="newInfoWindow.offset[0]" type="number" />
        </div>
        <div class="form-group">
          <label>Y偏移:</label>
          <input v-model.number="newInfoWindow.offset[1]" type="number" />
        </div>
      </div>
      <div class="form-options">
        <label class="checkbox-label">
          <input v-model="newInfoWindow.closable" type="checkbox" />
          <span>可关闭</span>
        </label>
        <div class="select-group">
          <label>关联标记:</label>
          <select v-model="newInfoWindow.markerId">
            <option value="">无关联</option>
            <option v-for="marker in markers" :key="marker.id" :value="marker.id">
              {{ marker.title || `标记 ${marker.id.slice(-6)}` }}
            </option>
          </select>
        </div>
      </div>
      <div class="form-actions">
        <button @click="addNewInfoWindow" class="action-btn primary">添加</button>
        <button @click="addInfoWindowAtCenter" class="action-btn secondary">在中心添加</button>
      </div>
    </div>

    <!-- 信息窗列表 -->
    <div class="infowindow-list">
      <h4>信息窗列表</h4>
      <div v-if="infoWindows.length === 0" class="empty-state">
        暂无信息窗
      </div>
      <div v-else class="infowindow-items">
        <div
          v-for="infoWindow in infoWindows"
          :key="infoWindow.id"
          class="infowindow-item"
          :class="{
            active: activeInfoWindow?.id === infoWindow.id,
            hidden: infoWindow.visible === false
          }"
          @click="selectInfoWindow(infoWindow)"
        >
          <div class="infowindow-info">
            <div class="infowindow-title">
              {{ infoWindow.title || `信息窗 ${infoWindow.id.slice(-6)}` }}
            </div>
            <div class="infowindow-position">
              {{ infoWindow.position.lat.toFixed(6) }}, {{ infoWindow.position.lng.toFixed(6) }}
            </div>
            <div class="infowindow-content">
              {{ getContentPreview(infoWindow.content) }}
            </div>
            <div v-if="infoWindow.markerId" class="infowindow-marker">
              关联标记: {{ getMarkerTitle(infoWindow.markerId) }}
            </div>
          </div>
          <div class="infowindow-actions">
            <button
              @click.stop="toggleInfoWindow(infoWindow.id)"
              class="icon-btn"
              :title="infoWindow.visible === false ? '显示' : '隐藏'"
            >
              {{ infoWindow.visible === false ? '👁️' : '🙈' }}
            </button>
            <button
              @click.stop="editInfoWindow(infoWindow)"
              class="icon-btn"
              title="编辑"
            >
              ✏️
            </button>
            <button
              @click.stop="removeInfoWindow(infoWindow.id)"
              class="icon-btn danger"
              title="删除"
            >
              🗑️
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑信息窗对话框 -->
    <div v-if="editingInfoWindow" class="edit-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h4>编辑信息窗</h4>
          <button @click="cancelEdit" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>标题:</label>
            <input v-model="editForm.title" type="text" />
          </div>
          <div class="form-group">
            <label>内容:</label>
            <textarea v-model="editForm.content as string" rows="4"></textarea>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>纬度:</label>
              <input v-model.number="editForm.position!.lat" type="number" step="0.000001" />
            </div>
            <div class="form-group">
              <label>经度:</label>
              <input v-model.number="editForm.position!.lng" type="number" step="0.000001" />
            </div>
          </div>
          <div class="form-options">
            <label class="checkbox-label">
              <input v-model="editForm.closable" type="checkbox" />
              <span>可关闭</span>
            </label>
            <label class="checkbox-label">
              <input v-model="editForm.visible" type="checkbox" />
              <span>可见</span>
            </label>
          </div>
        </div>
        <div class="modal-actions">
          <button @click="saveEdit" class="action-btn primary">保存</button>
          <button @click="cancelEdit" class="action-btn secondary">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { InfoWindowData } from '../composables/useInfoWindows';
import type { MarkerData } from '../composables/useMapMarkers';

// Props
interface Props {
  infoWindows: InfoWindowData[];
  activeInfoWindow: InfoWindowData | null;
  infoWindowCount: number;
  visibleInfoWindowCount: number;
  markers: MarkerData[];
  mapCenter?: { lat: number; lng: number };
}

const props = defineProps<Props>();

// Emits
interface Emits {
  (e: 'add-infowindow', infoWindow: Omit<InfoWindowData, 'id'>): void;
  (e: 'remove-infowindow', id: string): void;
  (e: 'update-infowindow', id: string, updates: Partial<InfoWindowData>): void;
  (e: 'open-infowindow', id: string): void;
  (e: 'close-infowindow', id: string): void;
  (e: 'close-all-infowindows'): void;
  (e: 'clear-infowindows'): void;
  (e: 'toggle-infowindow', id: string): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const showAddForm = ref(false);
const editingInfoWindow = ref<InfoWindowData | null>(null);

// 新信息窗表单数据
const newInfoWindow = ref({
  title: '',
  content: '',
  position: { lat: 0, lng: 0 },
  offset: [0, 0] as [number, number],
  closable: true,
  markerId: ''
});

// 编辑表单数据
const editForm = ref<Partial<InfoWindowData>>({});

// 重置新信息窗表单
const resetNewInfoWindowForm = () => {
  newInfoWindow.value = {
    title: '',
    content: '',
    position: { lat: props.mapCenter?.lat || 0, lng: props.mapCenter?.lng || 0 },
    offset: [0, 0],
    closable: true,
    markerId: ''
  };
};

// 添加新信息窗
const addNewInfoWindow = () => {
  const infoWindowData: Omit<InfoWindowData, 'id'> = {
    title: newInfoWindow.value.title || undefined,
    content: newInfoWindow.value.content,
    position: newInfoWindow.value.position,
    offset: newInfoWindow.value.offset,
    closable: newInfoWindow.value.closable,
    markerId: newInfoWindow.value.markerId || undefined
  };

  emit('add-infowindow', infoWindowData);
  resetNewInfoWindowForm();
  showAddForm.value = false;
};

// 在地图中心添加信息窗
const addInfoWindowAtCenter = () => {
  if (props.mapCenter) {
    newInfoWindow.value.position = { ...props.mapCenter };
    addNewInfoWindow();
  }
};

// 删除信息窗
const removeInfoWindow = (id: string) => {
  if (confirm('确定要删除这个信息窗吗？')) {
    emit('remove-infowindow', id);
  }
};

// 清空所有信息窗
const clearAllInfoWindows = () => {
  if (confirm('确定要清空所有信息窗吗？')) {
    emit('clear-infowindows');
  }
};

// 关闭所有信息窗
const closeAllInfoWindows = () => {
  emit('close-all-infowindows');
};

// 切换信息窗显示状态
const toggleInfoWindow = (id: string) => {
  emit('toggle-infowindow', id);
};

// 选择信息窗
const selectInfoWindow = (infoWindow: InfoWindowData) => {
  if (infoWindow.visible === false) {
    emit('open-infowindow', infoWindow.id);
  }
};

// 编辑信息窗
const editInfoWindow = (infoWindow: InfoWindowData) => {
  editingInfoWindow.value = infoWindow;
  editForm.value = { ...infoWindow };
};

// 保存编辑
const saveEdit = () => {
  if (editingInfoWindow.value && editForm.value) {
    emit('update-infowindow', editingInfoWindow.value.id, editForm.value);
    cancelEdit();
  }
};

// 取消编辑
const cancelEdit = () => {
  editingInfoWindow.value = null;
  editForm.value = {};
};

// 获取内容预览
const getContentPreview = (content: string | HTMLElement): string => {
  if (typeof content === 'string') {
    return content.length > 50 ? content.substring(0, 50) + '...' : content;
  }
  return '[HTML内容]';
};

// 获取标记标题
const getMarkerTitle = (markerId: string): string => {
  const marker = props.markers.find(m => m.id === markerId);
  return marker?.title || `标记 ${markerId.slice(-6)}`;
};

// 初始化表单
resetNewInfoWindowForm();
</script>

<style scoped>
.infowindow-panel {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.panel-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
}

.panel-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #6c757d;
  text-align: right;
}

.panel-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.action-btn.primary {
  background: #007bff;
  color: white;
}

.action-btn.primary:hover {
  background: #0056b3;
}

.action-btn.secondary {
  background: #6c757d;
  color: white;
}

.action-btn.secondary:hover {
  background: #545b62;
}

.action-btn.danger {
  background: #dc3545;
  color: white;
}

.action-btn.danger:hover {
  background: #c82333;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.add-form {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
}

.add-form h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 14px;
}

.form-group {
  margin-bottom: 12px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
}

.form-group textarea {
  resize: vertical;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.form-options {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6c757d;
  cursor: pointer;
}

.select-group {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6c757d;
}

.select-group select {
  width: auto;
  min-width: 120px;
}

.form-actions {
  display: flex;
  gap: 8px;
}

.infowindow-list {
  flex: 1;
  min-height: 0;
}

.infowindow-list h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  color: #6c757d;
  padding: 20px;
  font-style: italic;
}

.infowindow-items {
  max-height: 300px;
  overflow-y: auto;
}

.infowindow-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.infowindow-item:hover {
  background: #f8f9fa;
}

.infowindow-item.active {
  border-color: #28a745;
  background: #e8f5e8;
}

.infowindow-item.hidden {
  opacity: 0.5;
}

.infowindow-info {
  flex: 1;
}

.infowindow-title {
  font-weight: 500;
  color: #495057;
  font-size: 13px;
  margin-bottom: 4px;
}

.infowindow-position {
  font-size: 11px;
  color: #6c757d;
  font-family: monospace;
  margin-bottom: 4px;
}

.infowindow-content {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
  margin-bottom: 4px;
}

.infowindow-marker {
  font-size: 11px;
  color: #007bff;
  font-style: italic;
}

.infowindow-actions {
  display: flex;
  gap: 4px;
}

.icon-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: background 0.3s;
}

.icon-btn:hover {
  background: #e9ecef;
}

.icon-btn.danger:hover {
  background: #f8d7da;
}

.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #dee2e6;
}

.modal-header h4 {
  margin: 0;
  color: #495057;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
}

.modal-body {
  padding: 16px;
}

.modal-actions {
  display: flex;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #dee2e6;
  justify-content: flex-end;
}
</style>
