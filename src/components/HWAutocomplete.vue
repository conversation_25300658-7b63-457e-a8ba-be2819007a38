<template>
  <div class="hw-autocomplete">
    <input
      ref="inputRef"
      v-model="inputValue"
      :type="type"
      :placeholder="placeholder"
      :class="['autocomplete-input', inputClass]"
      :disabled="disabled"
      @input="onInput"
      @focus="onFocus"
      @blur="onBlur"
      @keyup.enter="onEnter"
      v-bind="$attrs"
    />

    <!-- 状态指示器 -->
    <div v-if="status" class="autocomplete-status" :class="status.type">
      <span class="status-icon">
        {{ status.type === 'success' ? '✓' : status.type === 'error' ? '✗' : 'ℹ' }}
      </span>
      <span class="status-text">{{ status.message }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { HuaweiSearchService } from '../services/search-service'
import type { LatLng, SearchResult } from '../types/search'

// Props
interface Props {
  modelValue?: string
  placeholder?: string
  type?: string
  disabled?: boolean
  inputClass?: string
  location?: LatLng
  radius?: number
  language?: string
  maxHeight?: string
  poiType?: string
  countries?: string[]
  countryCode?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '搜索地点、地址或POI',
  type: 'text',
  disabled: false,
  inputClass: '',
  radius: 5000,
  language: 'zh-CN',
  maxHeight: '200px',
  poiType: 'ESTABLISHMENT'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'site-selected': [site: any, result: SearchResult]
  'input': [value: string]
  'focus': [event: FocusEvent]
  'blur': [event: FocusEvent]
  'enter': [value: string]
  'status-change': [status: { type: string; message: string } | null]
}>()

// 响应式数据
const inputRef = ref<HTMLInputElement>()
const inputValue = ref(props.modelValue)
const autocompleteInstance = ref<any>(null)
const searchService = new HuaweiSearchService()
const status = ref<{ type: 'success' | 'error' | 'info'; message: string } | null>(null)

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue
})

// 监听 inputValue 变化
watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// 监听状态变化
watch(status, (newStatus) => {
  emit('status-change', newStatus)
})

// 事件处理
const onInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  inputValue.value = target.value
  emit('input', target.value)
}

const onFocus = (event: FocusEvent) => {
  emit('focus', event)
  if (!autocompleteInstance.value) {
    initAutocomplete()
  }
}

const onBlur = (event: FocusEvent) => {
  emit('blur', event)
}

const onEnter = () => {
  emit('enter', inputValue.value)
}

// 初始化自动补全
const initAutocomplete = async () => {
  await nextTick()

  if (!inputRef.value) {
    setStatus('error', '输入框未找到')
    return
  }

  if (!window.HWMapJsSDK) {
    console.warn('华为地图SDK未加载，等待加载...')
    setStatus('info', '等待华为地图SDK加载...')

    // 等待SDK加载，最多等待10秒
    let attempts = 0
    const maxAttempts = 100 // 10秒，每100ms检查一次

    const checkSDK = () => {
      attempts++
      if (window.HWMapJsSDK) {
        console.log('华为地图SDK加载完成，初始化自动补全')
        initAutocompleteWithSDK()
      } else if (attempts < maxAttempts) {
        setTimeout(checkSDK, 100)
      } else {
        setStatus('error', '华为地图SDK加载超时')
      }
    }

    setTimeout(checkSDK, 100)
    return
  }

  initAutocompleteWithSDK()
}

// 使用SDK初始化自动补全
const initAutocompleteWithSDK = () => {
  try {
    const options: any = {
      language: props.language,
      maxHeight: props.maxHeight,
      radius: props.radius
    }

    if (props.location) {
      options.location = props.location
    }

    if (props.poiType) {
      options.poiType = props.poiType
    }

    if (props.countries && props.countries.length > 0) {
      options.countries = props.countries
    }

    if (props.countryCode) {
      options.countryCode = props.countryCode
    }

    const result = searchService.createAutocomplete(inputRef.value, options)

    if (result && result.instance) {
      autocompleteInstance.value = result
      setStatus('success', '自动补全已启用')

      // 监听选择事件
      result.addListener('site_changed', () => {
        const site = result.getSite()
        if (site) {
          console.log('HWAutocomplete 选中的地点:', site)

          // 更新输入框内容
          inputValue.value = site.name || site.formatAddress || ''

          // 转换为标准的搜索结果格式
          const searchResult: SearchResult = {
            id: site.siteId || Math.random().toString(36),
            name: site.name || '',
            address: site.formatAddress || '',
            location: {
              lat: parseFloat(site.location.lat),
              lng: parseFloat(site.location.lng)
            },
            type: site.poi?.poiTypes?.[0] || 'unknown',
            distance: 0,
            phone: site.poi?.phone,
            rating: site.poi?.rating ? parseFloat(site.poi.rating) : undefined
          }

          emit('site-selected', site, searchResult)
        }
      })

      // 3秒后隐藏成功状态
      setTimeout(() => {
        if (status.value?.type === 'success') {
          status.value = null
        }
      }, 3000)
    } else {
      throw new Error('创建自动补全实例失败')
    }
  } catch (error) {
    console.error('初始化自动补全失败:', error)
    setStatus('error', '自动补全初始化失败')

    // 5秒后隐藏错误状态
    setTimeout(() => {
      if (status.value?.type === 'error') {
        status.value = null
      }
    }, 5000)
  }
}

// 设置状态
const setStatus = (type: 'success' | 'error' | 'info', message: string) => {
  status.value = { type, message }
}

// 销毁自动补全实例
const destroyAutocomplete = () => {
  if (autocompleteInstance.value) {
    try {
      autocompleteInstance.value.destroy()
      autocompleteInstance.value = null
    } catch (error) {
      console.warn('销毁自动补全实例失败:', error)
    }
  }
}

// 公开方法
const focus = () => {
  inputRef.value?.focus()
}

const blur = () => {
  inputRef.value?.blur()
}

const clear = () => {
  inputValue.value = ''
}

// 生命周期
onMounted(() => {
  // 延迟初始化，确保DOM已渲染
  setTimeout(() => {
    initAutocomplete()
  }, 100)
})

onUnmounted(() => {
  destroyAutocomplete()
})

// 暴露方法给父组件
defineExpose({
  focus,
  blur,
  clear,
  initAutocomplete,
  destroyAutocomplete
})
</script>

<style scoped>
.hw-autocomplete {
  position: relative;
}

.autocomplete-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.autocomplete-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.autocomplete-input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.autocomplete-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  margin-top: 4px;
  border-radius: 4px;
  font-size: 12px;
  animation: fadeIn 0.3s ease-in-out;
  position: relative;
  z-index: 1000;
}

.autocomplete-status.success {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

.autocomplete-status.error {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
}

.autocomplete-status.info {
  background: #f0f9ff;
  border: 1px solid #91d5ff;
  color: #1890ff;
}

.status-icon {
  font-weight: bold;
  font-size: 14px;
}

.status-text {
  flex: 1;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
