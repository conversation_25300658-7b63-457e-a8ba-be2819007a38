<template>
  <div
    ref="mapContainer"
    class="huawei-map-container"
    :style="{ width: width, height: height }"
  >
    <div v-if="loading" class="map-loading">
      <div class="loading-spinner"></div>
      <p>地图加载中...</p>
    </div>
    <div v-if="error" class="map-error">
      <p>{{ error }}</p>
      <button @click="retryLoad" class="retry-button">重试</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { loadHuaweiMapAPI, isHuaweiMapAPILoaded } from '../utils/map-loader';
import { defaultMapConfig } from '../config/map-config';
import { useMapEvents, type MapEventHandlers } from '../composables/useMapEvents';
import { useMapMarkers, type MarkerData, type MarkerEventHandlers } from '../composables/useMapMarkers';
import { useInfoWindows, type InfoWindowData, type InfoWindowEventHandlers } from '../composables/useInfoWindows';

// Props定义
interface Props {
  // 地图容器尺寸
  width?: string;
  height?: string;

  // 地图基础配置
  center?: { lat: number; lng: number };
  zoom?: number;
  language?: string;
  sourceType?: 'vector' | 'raster';
  mapType?: 'ROADMAP' | 'TERRAIN';

  // 缩放级别限制
  minZoom?: number;
  maxZoom?: number;

  // 控件显示配置
  copyrightControl?: boolean;
  locationControl?: boolean;
  navigationControl?: boolean;
  rotateControl?: boolean;
  scaleControl?: boolean;
  zoomControl?: boolean;
  zoomSlider?: boolean;

  // 样式配置
  logoPosition?: 'BOTTOM_LEFT' | 'BOTTOM_RIGHT' | 'TOP_LEFT' | 'TOP_RIGHT';
  presetStyleId?: 'standard' | 'night' | 'simple';
  opacity?: number;

  // API配置
  apiKey?: string;

  // 事件配置
  enableEventMonitor?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '400px',
  center: () => defaultMapConfig.defaultCenter,
  zoom: defaultMapConfig.defaultZoom,
  language: defaultMapConfig.defaultLanguage,
  sourceType: defaultMapConfig.defaultSourceType,
  mapType: 'ROADMAP',
  minZoom: 2,
  maxZoom: 22,
  copyrightControl: false,
  locationControl: false,
  navigationControl: false,
  rotateControl: false,
  scaleControl: false,
  zoomControl: true,
  zoomSlider: false,
  logoPosition: 'BOTTOM_LEFT',
  presetStyleId: 'standard',
  opacity: 1,
  enableEventMonitor: false
});

// Emits定义
interface Emits {
  (e: 'map-ready', map: any): void;
  (e: 'map-click', event: any): void;
  (e: 'map-dblclick', event: any): void;
  (e: 'map-rightclick', event: any): void;
  (e: 'map-mousemove', event: any): void;
  (e: 'map-mousedown', event: any): void;
  (e: 'map-mouseup', event: any): void;
  (e: 'map-drag', event: any): void;
  (e: 'move-start'): void;
  (e: 'move-end'): void;
  (e: 'center-changed', center: { lat: number; lng: number }): void;
  (e: 'zoom-changed', zoom: number): void;
  (e: 'heading-changed', heading: number): void;
  (e: 'map-error', error: string): void;
  // 标记事件
  (e: 'marker-click', marker: MarkerData, event: any): void;
  (e: 'marker-dblclick', marker: MarkerData, event: any): void;
  (e: 'marker-rightclick', marker: MarkerData, event: any): void;
  (e: 'marker-dragstart', marker: MarkerData, event: any): void;
  (e: 'marker-drag', marker: MarkerData, event: any): void;
  (e: 'marker-dragend', marker: MarkerData, event: any): void;
  // 信息窗事件
  (e: 'infowindow-open', infoWindow: InfoWindowData): void;
  (e: 'infowindow-close', infoWindow: InfoWindowData): void;
  (e: 'infowindow-content-change', infoWindow: InfoWindowData, content: string | HTMLElement): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const mapContainer = ref<HTMLElement>();
const loading = ref(true);
const error = ref<string>('');
const map = ref<any>();

// 事件处理器配置
const eventHandlers: MapEventHandlers = {
  onClick: (event) => emit('map-click', event),
  onDoubleClick: (event) => emit('map-dblclick', event),
  onRightClick: (event) => emit('map-rightclick', event),
  onMouseMove: (event) => emit('map-mousemove', event),
  onMouseDown: (event) => emit('map-mousedown', event),
  onMouseUp: (event) => emit('map-mouseup', event),
  onDrag: (event) => emit('map-drag', event),
  onMoveStart: () => emit('move-start'),
  onMoveEnd: () => emit('move-end'),
  onCenterChanged: (center) => emit('center-changed', center),
  onZoomChanged: (zoom) => emit('zoom-changed', zoom),
  onHeadingChanged: (heading) => emit('heading-changed', heading)
};

// 使用事件管理组合式函数
const {
  eventHistory,
  currentCenter,
  currentZoom,
  currentHeading,
  bindMapEvents,
  unbindAllEvents,
  clearHistory
} = useMapEvents(map, props.enableEventMonitor ? eventHandlers : undefined);

// 标记事件处理器配置
const markerEventHandlers: MarkerEventHandlers = {
  onClick: (marker, event) => {
    emit('marker-click', marker, event);
    // 如果标记有关联的信息窗，显示它
    const markerInstance = getMarker(marker.id);
    if (markerInstance) {
      showMarkerInfoWindow(marker.id, markerInstance);
    }
  },
  onDoubleClick: (marker, event) => emit('marker-dblclick', marker, event),
  onRightClick: (marker, event) => emit('marker-rightclick', marker, event),
  onDragStart: (marker, event) => emit('marker-dragstart', marker, event),
  onDrag: (marker, event) => emit('marker-drag', marker, event),
  onDragEnd: (marker, event) => emit('marker-dragend', marker, event)
};

// 使用标记管理组合式函数
const {
  markers,
  visibleMarkers,
  selectedMarker,
  markerCount,
  visibleMarkerCount,
  addMarker,
  addMarkers,
  removeMarker,
  removeMarkers,
  clearMarkers,
  updateMarker,
  getMarker,
  toggleMarkerVisibility,
  selectMarker,
  deselectMarker,
  fitMarkersToView
} = useMapMarkers(map, markerEventHandlers);

// 信息窗事件处理器配置
const infoWindowEventHandlers: InfoWindowEventHandlers = {
  onOpen: (infoWindow) => emit('infowindow-open', infoWindow),
  onClose: (infoWindow) => emit('infowindow-close', infoWindow),
  onContentChange: (infoWindow, content) => emit('infowindow-content-change', infoWindow, content)
};

// 使用信息窗管理组合式函数
const {
  infoWindows,
  visibleInfoWindows,
  activeInfoWindow,
  infoWindowCount,
  visibleInfoWindowCount,
  addInfoWindow,
  openInfoWindow,
  closeInfoWindow,
  closeAllInfoWindows,
  removeInfoWindow,
  clearInfoWindows,
  updateInfoWindow,
  getInfoWindow,
  createInfoWindowForMarker,
  showMarkerInfoWindow,
  hideMarkerInfoWindow,
  toggleInfoWindow,
  setInfoWindowContent,
  moveInfoWindow
} = useInfoWindows(map, infoWindowEventHandlers);

// 初始化地图
const initMap = async () => {
  try {
    loading.value = true;
    error.value = '';

    // 确保容器存在
    if (!mapContainer.value) {
      throw new Error('地图容器未找到');
    }

    // 加载华为地图API
    await loadHuaweiMapAPI(props.apiKey);

    // 等待DOM更新
    await nextTick();

    // 创建地图配置
    const mapOptions: any = {
      center: props.center,
      zoom: props.zoom,
      language: props.language,
      sourceType: props.sourceType,
      mapType: props.mapType,
      minZoom: props.minZoom,
      maxZoom: props.maxZoom,
      copyrightControl: props.copyrightControl,
      locationControl: props.locationControl,
      navigationControl: props.navigationControl,
      rotateControl: props.rotateControl,
      scaleControl: props.scaleControl,
      zoomControl: props.zoomControl,
      zoomSlider: props.zoomSlider,
      logoPosition: props.logoPosition,
      presetStyleId: props.presetStyleId,
      // 优化渲染质量
      rasterPreload: true,  // 启用栅格预加载
      pixelRatio: window.devicePixelRatio || 1  // 支持高分辨率屏幕
    };

    // 创建地图实例
    map.value = new window.HWMapJsSDK.HWMap(mapContainer.value, mapOptions);

    // 设置透明度
    if (props.opacity !== 1) {
      map.value.setOpacity(props.opacity);
    }

    // 确保地图适应容器大小
    // 使用多次resize确保地图正确显示
    setTimeout(() => {
      if (map.value) {
        map.value.resize();
      }
    }, 100);

    setTimeout(() => {
      if (map.value) {
        map.value.resize();
      }
    }, 300);

    setTimeout(() => {
      if (map.value) {
        map.value.resize();
      }
    }, 500);

    // 绑定事件（如果启用了事件监控）
    if (props.enableEventMonitor) {
      bindMapEvents();
    } else {
      // 绑定基础事件
      bindBasicEvents();
    }

    loading.value = false;
    emit('map-ready', map.value);

  } catch (err) {
    loading.value = false;
    const errorMessage = err instanceof Error ? err.message : '地图初始化失败';
    error.value = errorMessage;
    emit('map-error', errorMessage);
    console.error('华为地图初始化失败:', err);
  }
};

// 绑定基础地图事件（不使用事件监控时）
const bindBasicEvents = () => {
  if (!map.value) return;

  // 点击事件
  map.value.on('click', (event: any) => {
    emit('map-click', event);
  });

  // 双击事件
  map.value.on('dblclick', (event: any) => {
    emit('map-dblclick', event);
  });

  // 右键点击事件
  map.value.on('contextmenu', (event: any) => {
    emit('map-rightclick', event);
  });

  // 中心点变化事件
  map.value.onCenterChanged(() => {
    const center = map.value.getCenter();
    emit('center-changed', center);
  });

  // 缩放级别变化事件
  map.value.onZoomChanged(() => {
    const zoom = map.value.getZoom();
    emit('zoom-changed', zoom);
  });
};

// 重试加载
const retryLoad = () => {
  initMap();
};

// 监听props变化
watch(() => props.center, (newCenter) => {
  if (map.value && newCenter) {
    map.value.setCenter(newCenter);
  }
});

watch(() => props.zoom, (newZoom) => {
  if (map.value && newZoom) {
    map.value.setZoom(newZoom);
  }
});

watch(() => props.opacity, (newOpacity) => {
  if (map.value && newOpacity !== undefined) {
    map.value.setOpacity(newOpacity);
  }
});

// 监听地图类型变化 - 重新初始化以确保配置正确
watch(() => props.mapType, (newValue, oldValue) => {
  if (map.value && newValue !== oldValue) {
    console.log('地图类型发生变化，重新初始化地图:', newValue);
    initMap();
  }
});

// 监听容器尺寸变化
watch([() => props.width, () => props.height], () => {
  if (map.value) {
    nextTick(() => {
      setTimeout(() => {
        if (map.value) {
          map.value.resize();
        }
      }, 100);
    });
  }
});

// 监听预设样式变化
watch(() => props.presetStyleId, (newStyleId) => {
  if (map.value && newStyleId) {
    console.log('设置预设样式:', newStyleId);
    map.value.setPresetStyleId(newStyleId);
  }
});

// 监听Logo位置变化
watch(() => props.logoPosition, (newPosition) => {
  if (map.value && newPosition) {
    console.log('设置Logo位置:', newPosition);
    map.value.setLogoPosition(newPosition);
  }
});

// 监听控件配置变化
watch(() => props.copyrightControl, (newValue) => {
  if (map.value && newValue !== undefined) {
    console.log('设置版权控件:', newValue);
    map.value.setCopyrightControl(newValue);
  }
});

watch(() => props.locationControl, (newValue) => {
  if (map.value && newValue !== undefined) {
    console.log('设置定位控件:', newValue);
    map.value.setLocationControl(newValue);
  }
});

watch(() => props.navigationControl, (newValue) => {
  if (map.value && newValue !== undefined) {
    console.log('设置导航控件:', newValue);
    map.value.setNavigationControl(newValue);
  }
});

watch(() => props.rotateControl, (newValue) => {
  if (map.value && newValue !== undefined) {
    console.log('设置旋转控件:', newValue);
    map.value.setRotateControl(newValue);
  }
});

watch(() => props.scaleControl, (newValue) => {
  if (map.value && newValue !== undefined) {
    console.log('设置比例尺控件:', newValue);
    map.value.setScaleControl(newValue);
  }
});

watch(() => props.zoomControl, (newValue) => {
  if (map.value && newValue !== undefined) {
    console.log('设置缩放控件:', newValue);
    map.value.setZoomControl(newValue);
  }
});

watch(() => props.zoomSlider, (newValue) => {
  if (map.value && newValue !== undefined) {
    console.log('设置缩放条:', newValue);
    map.value.setZoomSlider(newValue);
  }
});

// 监听需要重新初始化的配置项
watch(() => props.sourceType, (newValue, oldValue) => {
  if (map.value && newValue !== oldValue) {
    console.log('瓦片类型发生变化，重新初始化地图:', newValue);
    initMap();
  }
});

watch(() => props.language, (newValue, oldValue) => {
  if (map.value && newValue !== oldValue) {
    console.log('语言设置发生变化，重新初始化地图:', newValue);
    initMap();
  }
});

watch(() => props.minZoom, (newValue, oldValue) => {
  if (map.value && newValue !== oldValue) {
    console.log('最小缩放级别发生变化，重新初始化地图:', newValue);
    initMap();
  }
});

watch(() => props.maxZoom, (newValue, oldValue) => {
  if (map.value && newValue !== oldValue) {
    console.log('最大缩放级别发生变化，重新初始化地图:', newValue);
    initMap();
  }
});

// 窗口大小变化处理
const handleResize = () => {
  if (map.value) {
    map.value.resize();
  }
};

// 生命周期
onMounted(() => {
  initMap();
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleResize);

  // 清理地图实例
  if (map.value) {
    // 华为地图没有明确的销毁方法，这里只是清空引用
    map.value = undefined;
  }
});

// 暴露地图实例和功能给父组件
defineExpose({
  map: map,
  getMap: () => map.value,
  reload: initMap,
  // 事件相关
  eventHistory,
  currentCenter,
  currentZoom,
  currentHeading,
  clearEventHistory: clearHistory,
  // 标记相关
  markers,
  visibleMarkers,
  selectedMarker,
  markerCount,
  visibleMarkerCount,
  addMarker,
  addMarkers,
  removeMarker,
  removeMarkers,
  clearMarkers,
  updateMarker,
  getMarker,
  toggleMarkerVisibility,
  selectMarker,
  deselectMarker,
  fitMarkersToView,
  // 信息窗相关
  infoWindows,
  visibleInfoWindows,
  activeInfoWindow,
  infoWindowCount,
  visibleInfoWindowCount,
  addInfoWindow,
  openInfoWindow,
  closeInfoWindow,
  closeAllInfoWindows,
  removeInfoWindow,
  clearInfoWindows,
  updateInfoWindow,
  getInfoWindow,
  createInfoWindowForMarker,
  showMarkerInfoWindow,
  hideMarkerInfoWindow,
  toggleInfoWindow,
  setInfoWindowContent,
  moveInfoWindow
});
</script>

<style scoped>
.huawei-map-container {
  position: relative;
  background-color: #f0f0f0;
  overflow: hidden;
  box-sizing: border-box;
  max-width: 100%;
  max-height: 100%;
  /* 优化高分辨率显示 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.map-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 1000;
  color: #e74c3c;
}

.retry-button {
  margin-top: 16px;
  padding: 8px 16px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.retry-button:hover {
  background-color: #2980b9;
}
</style>
