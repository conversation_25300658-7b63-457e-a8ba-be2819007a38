// 华为地图配置文件

// 华为地图API配置
export interface HuaweiMapConfig {
  apiKey: string;
  apiUrl: string;
  defaultCenter: { lat: number; lng: number };
  defaultZoom: number;
  defaultLanguage: string;
  defaultSourceType: 'vector' | 'raster';
}

// 读取API密钥
function getApiKey(): string {
  // 从环境变量获取API密钥
  // 请在 .env 文件中设置 VITE_HUAWEI_MAP_API_KEY
  const apiKey = import.meta.env.VITE_HUAWEI_MAP_API_KEY;
  if (!apiKey) {
    console.warn('华为地图API密钥未配置，请在环境变量中设置 VITE_HUAWEI_MAP_API_KEY');
    return '';
  }
  return apiKey;
}

// 默认配置
export const defaultMapConfig: HuaweiMapConfig = {
  apiKey: getApiKey(),
  apiUrl: import.meta.env.VITE_HUAWEI_MAP_API_URL || 'https://mapapi.cloud.huawei.com/mapjs/v1/api/js',
  defaultCenter: {
    lat: Number(import.meta.env.VITE_DEFAULT_MAP_CENTER_LAT) || 23.130415047434678,
    lng: Number(import.meta.env.VITE_DEFAULT_MAP_CENTER_LNG) || 113.32380294799805
  }, // 广州
  defaultZoom: Number(import.meta.env.VITE_DEFAULT_MAP_ZOOM) || 10,
  defaultLanguage: 'zh',
  defaultSourceType: 'vector'
};

// 地图样式预设
export const mapStyles = {
  standard: 'standard',
  night: 'night',
  simple: 'simple'
} as const;

// 地图类型
export const mapTypes = {
  ROADMAP: 'ROADMAP',
  TERRAIN: 'TERRAIN'
} as const;

// Logo位置选项
export const logoPositions = {
  BOTTOM_LEFT: 'BOTTOM_LEFT',
  BOTTOM_RIGHT: 'BOTTOM_RIGHT',
  TOP_LEFT: 'TOP_LEFT',
  TOP_RIGHT: 'TOP_RIGHT'
} as const;

// 标记动画类型
export const markerAnimations = {
  DROP: 'DROP',
  BOUNCE: 'BOUNCE'
} as const;

// 常用城市坐标
export const commonCities = {
  beijing: { lat: 39.9042, lng: 116.4074 },
  shanghai: { lat: 31.2304, lng: 121.4737 },
  guangzhou: { lat: 23.1291, lng: 113.2644 },
  shenzhen: { lat: 22.5431, lng: 114.0579 },
  hangzhou: { lat: 30.2741, lng: 120.1551 },
  nanjing: { lat: 32.0603, lng: 118.7969 }
} as const;
