// 华为地图API加载器
import { defaultMapConfig } from '../config/map-config';

// API加载状态
let isLoading = false;
let isLoaded = false;
let loadPromise: Promise<void> | null = null;

/**
 * 动态加载华为地图API
 * @param apiKey API密钥，如果不提供则使用默认配置
 * @returns Promise<void>
 */
export function loadHuaweiMapAPI(apiKey?: string): Promise<void> {
  // 如果已经加载完成，直接返回resolved的Promise
  if (isLoaded && window.HWMapJsSDK) {
    return Promise.resolve();
  }

  // 如果正在加载，返回现有的Promise
  if (isLoading && loadPromise) {
    return loadPromise;
  }

  // 开始加载
  isLoading = true;
  const key = apiKey || defaultMapConfig.apiKey;
  
  loadPromise = new Promise((resolve, reject) => {
    // 创建script标签
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    
    // 设置全局回调函数
    const callbackName = `initHuaweiMap_${Date.now()}`;
    (window as any)[callbackName] = () => {
      isLoaded = true;
      isLoading = false;
      // 清理全局回调函数
      delete (window as any)[callbackName];
      resolve();
    };
    
    // 设置API URL
    script.src = `${defaultMapConfig.apiUrl}?callback=${callbackName}&key=${key}`;
    
    // 错误处理
    script.onerror = () => {
      isLoading = false;
      loadPromise = null;
      // 清理全局回调函数
      delete (window as any)[callbackName];
      reject(new Error('华为地图API加载失败'));
    };
    
    // 添加到页面
    document.head.appendChild(script);
  });

  return loadPromise;
}

/**
 * 检查华为地图API是否已加载
 * @returns boolean
 */
export function isHuaweiMapAPILoaded(): boolean {
  return isLoaded && !!window.HWMapJsSDK;
}

/**
 * 等待华为地图API加载完成
 * @param timeout 超时时间（毫秒），默认10秒
 * @returns Promise<void>
 */
export function waitForHuaweiMapAPI(timeout: number = 10000): Promise<void> {
  return new Promise((resolve, reject) => {
    if (isHuaweiMapAPILoaded()) {
      resolve();
      return;
    }

    const startTime = Date.now();
    const checkInterval = setInterval(() => {
      if (isHuaweiMapAPILoaded()) {
        clearInterval(checkInterval);
        resolve();
      } else if (Date.now() - startTime > timeout) {
        clearInterval(checkInterval);
        reject(new Error('等待华为地图API加载超时'));
      }
    }, 100);
  });
}

/**
 * 重置加载状态（用于测试或重新加载）
 */
export function resetLoadState(): void {
  isLoading = false;
  isLoaded = false;
  loadPromise = null;
}
