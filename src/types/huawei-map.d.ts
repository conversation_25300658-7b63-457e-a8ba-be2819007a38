// 华为地图API类型声明文件
declare global {
  interface Window {
    HWMapJsSDK: typeof HWMapJsSDK;
    initMap?: () => void;
  }
}

// 华为地图SDK命名空间
declare namespace HWMapJsSDK {
  // 基础坐标类型
  interface LatLng {
    lat: number;
    lng: number;
  }

  // 坐标边界类型
  interface LatLngBounds {
    ne: LatLng;
    sw: LatLng;
  }

  // 地图配置选项
  interface MapOptions {
    center: LatLng;
    zoom: number;
    language?: string;
    sourceType?: 'vector' | 'raster';
    mapType?: 'ROADMAP' | 'TERRAIN';
    minZoom?: number;
    maxZoom?: number;
    copyrightControl?: boolean;
    locationControl?: boolean;
    navigationControl?: boolean;
    rotateControl?: boolean;
    scaleControl?: boolean;
    zoomControl?: boolean;
    zoomSlider?: boolean;
    logoPosition?: 'BOTTOM_LEFT' | 'BOTTOM_RIGHT' | 'TOP_LEFT' | 'TOP_RIGHT';
    presetStyleId?: 'standard' | 'night' | 'simple';
    rasterPreload?: boolean;
  }

  // 地图事件类型
  interface MapEvent {
    coordinate: [number, number];
    pixel: [number, number];
  }

  // 地图类
  class HWMap {
    constructor(container: HTMLElement, options: MapOptions);
    
    // 基础方法
    getCenter(): LatLng;
    setCenter(center: LatLng): void;
    getZoom(): number;
    setZoom(zoom: number): void;
    getBounds(): LatLngBounds;
    fitBounds(bounds: LatLngBounds): void;
    
    // 控制方法
    zoomIn(): void;
    zoomOut(): void;
    panTo(latLng: LatLng): void;
    panBy(x: number, y: number): void;
    resize(): void;
    
    // 样式设置
    setMapType(mapType: string): void;
    setPresetStyleId(styleId: string): void;
    setOpacity(opacity: number): void;
    
    // 控件设置
    setCopyrightControl(enabled: boolean): void;
    setLocationControl(enabled: boolean): void;
    setNavigationControl(enabled: boolean): void;
    setRotateControl(enabled: boolean): void;
    setScaleControl(enabled: boolean): void;
    setZoomControl(enabled: boolean): void;
    setZoomSlider(enabled: boolean): void;
    
    // 坐标转换
    fromScreenLocation(pixel: { x: number; y: number }): LatLng;
    toScreenLocation(latLng: LatLng): { x: number; y: number };
    
    // 事件处理
    on(event: string, callback: (event: MapEvent) => void): void;
    un(event: string, callback: (event: MapEvent) => void): void;
    onCenterChanged(callback: () => void): void;
    onZoomChanged(callback: () => void): void;
    onHeadingChanged(callback: () => void): void;
  }

  // 标记图标选项
  interface MarkerIconOption {
    url?: string;
    anchor?: [number, number];
    anchorUnit?: 'fraction' | 'pixels';
    scale?: number;
    rotation?: number;
    opacity?: number;
  }

  // 标记标签选项
  interface MarkerLabelOption {
    text: string;
    color?: string;
    fontSize?: string;
    fontFamily?: string;
    offsetX?: number;
    offsetY?: number;
    strokeColor?: string;
    strokeWeight?: number;
  }

  // 标记配置选项
  interface MarkerOptions {
    position: LatLng;
    map: HWMap;
    icon?: string | MarkerIconOption;
    label?: string | MarkerLabelOption;
    draggable?: boolean;
    animation?: 'DROP' | 'BOUNCE' | null;
    zIndex?: number;
    properties?: any;
  }

  // 标记类
  class HWMarker {
    constructor(options: MarkerOptions);
    
    getPosition(): LatLng;
    setPosition(position: LatLng): void;
    getMap(): HWMap;
    setMap(map: HWMap | null): void;
    getIcon(): string | MarkerIconOption;
    setIcon(icon: string | MarkerIconOption): void;
    getLabel(): string | MarkerLabelOption;
    setLabel(label: string | MarkerLabelOption): void;
    getDraggable(): boolean;
    setDraggable(draggable: boolean): void;
    getAnimation(): string;
    setAnimation(animation: 'DROP' | 'BOUNCE' | null): void;
    getZIndex(): number;
    setZIndex(zIndex: number): void;
    getProperties(): any;
    
    addListener(event: string, callback: (event: any) => void): void;
    removeListener(event: string, callback: (event: any) => void): void;
  }

  // 信息窗配置选项
  interface InfoWindowOptions {
    content?: string | HTMLElement;
    position: LatLng;
    map: HWMap;
    offset?: [number, number];
  }

  // 信息窗类
  class HWInfoWindow {
    constructor(options: InfoWindowOptions);
    
    getContent(): string | HTMLElement;
    setContent(content: string | HTMLElement): void;
    getPosition(): LatLng;
    setPosition(position: LatLng): void;
    open(marker?: HWMarker): void;
    close(): void;
    
    addListener(event: string, callback: (event: any) => void): void;
    removeListener(event: string, callback: (event: any) => void): void;
  }
}

export {};
