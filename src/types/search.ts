// 搜索和地理编码相关类型定义

// 基础坐标类型
export interface LatLng {
  lat: number;
  lng: number;
}

// 搜索选项
export interface SearchOptions {
  // 搜索区域中心点
  center?: LatLng;
  // 搜索半径（米）
  radius?: number;
  // 城市限制
  city?: string;
  // 返回结果数量限制
  limit?: number;
  // 搜索类型过滤
  types?: string[];
}

// 搜索结果
export interface SearchResult {
  // 唯一标识
  id: string;
  // 地点名称
  name: string;
  // 地址
  address: string;
  // 坐标
  location: LatLng;
  // POI类型
  type: string;
  // 距离（米）
  distance?: number;
  // 评分
  rating?: number;
  // 电话
  phone?: string;
  // 详细信息
  details?: Record<string, any>;
}

// 地理编码结果
export interface GeocodingResult {
  // 输入地址
  address: string;
  // 解析后的坐标
  location: LatLng;
  // 置信度
  confidence: number;
  // 地址组件
  components: AddressComponents;
}

// 逆地理编码结果
export interface ReverseGeocodingResult {
  // 输入坐标
  location: LatLng;
  // 格式化地址
  formattedAddress: string;
  // 地址组件
  components: AddressComponents;
  // 周边POI
  nearbyPOIs?: POIResult[];
}

// 地址组件
export interface AddressComponents {
  // 国家
  country?: string;
  // 省份
  province?: string;
  // 城市
  city?: string;
  // 区县
  district?: string;
  // 街道
  street?: string;
  // 门牌号
  streetNumber?: string;
  // 邮编
  postalCode?: string;
}

// POI结果
export interface POIResult {
  // POI ID
  id: string;
  // 名称
  name: string;
  // 类型
  type: string;
  // 坐标
  location: LatLng;
  // 地址
  address: string;
  // 距离
  distance: number;
  // 标签
  tags?: string[];
}

// 搜索建议结果
export interface SuggestionResult {
  // 建议文本
  text: string;
  // 类型
  type: 'place' | 'address' | 'poi';
  // 坐标（如果有）
  location?: LatLng;
  // 地址
  address?: string;
}

// 搜索服务接口
export interface SearchService {
  // 地点搜索
  searchPlace(query: string, options?: SearchOptions): Promise<SearchResult[]>;
  
  // 地理编码
  geocode(address: string): Promise<GeocodingResult>;
  
  // 逆地理编码
  reverseGeocode(lat: number, lng: number): Promise<ReverseGeocodingResult>;
  
  // 周边搜索
  searchNearby(center: LatLng, radius: number, keyword?: string): Promise<POIResult[]>;
  
  // 搜索建议
  getSuggestions(query: string): Promise<SuggestionResult[]>;
}

// 搜索事件类型
export interface SearchEvents {
  'search-start': (query: string) => void;
  'search-complete': (results: SearchResult[]) => void;
  'search-error': (error: Error) => void;
  'geocode-complete': (result: GeocodingResult) => void;
  'reverse-geocode-complete': (result: ReverseGeocodingResult) => void;
}
