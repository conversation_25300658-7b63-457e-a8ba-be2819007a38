<template>
  <div class="autocomplete-test">
    <h1>HWAutocomplete 组件测试</h1>
    
    <div class="test-section">
      <h2>基本自动补全测试</h2>
      <div class="test-item">
        <label>搜索地点：</label>
        <HWAutocomplete
          v-model="searchValue1"
          placeholder="输入地点名称，如：天安门"
          @site-selected="onSiteSelected1"
          @status-change="onStatusChange1"
        />
        <div class="result-display">
          <p><strong>输入值：</strong>{{ searchValue1 }}</p>
          <p><strong>状态：</strong>{{ status1?.message || '无' }}</p>
          <div v-if="selectedSite1">
            <p><strong>选中地点：</strong></p>
            <pre>{{ JSON.stringify(selectedSite1, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>带位置偏向的自动补全测试</h2>
      <div class="test-item">
        <label>搜索地点（北京地区）：</label>
        <HWAutocomplete
          v-model="searchValue2"
          placeholder="输入地点名称，优先显示北京地区结果"
          :location="{ lat: 39.9042, lng: 116.4074 }"
          :radius="10000"
          @site-selected="onSiteSelected2"
          @status-change="onStatusChange2"
        />
        <div class="result-display">
          <p><strong>输入值：</strong>{{ searchValue2 }}</p>
          <p><strong>状态：</strong>{{ status2?.message || '无' }}</p>
          <div v-if="selectedSite2">
            <p><strong>选中地点：</strong></p>
            <pre>{{ JSON.stringify(selectedSite2, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>仅地址类型的自动补全测试</h2>
      <div class="test-item">
        <label>搜索地址：</label>
        <HWAutocomplete
          v-model="searchValue3"
          placeholder="输入地址，如：北京市朝阳区"
          poi-type="ADDRESS"
          @site-selected="onSiteSelected3"
          @status-change="onStatusChange3"
        />
        <div class="result-display">
          <p><strong>输入值：</strong>{{ searchValue3 }}</p>
          <p><strong>状态：</strong>{{ status3?.message || '无' }}</p>
          <div v-if="selectedSite3">
            <p><strong>选中地点：</strong></p>
            <pre>{{ JSON.stringify(selectedSite3, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>控制按钮</h2>
      <div class="button-group">
        <button @click="clearAll">清空所有</button>
        <button @click="focusFirst">聚焦第一个</button>
        <button @click="testProgrammaticInput">程序化输入测试</button>
      </div>
    </div>

    <div class="test-section">
      <h2>事件日志</h2>
      <div class="event-log">
        <div
          v-for="(event, index) in eventLog"
          :key="index"
          class="log-item"
        >
          <span class="log-time">{{ event.time }}</span>
          <span class="log-message">{{ event.message }}</span>
        </div>
      </div>
      <button @click="clearLog">清除日志</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import HWAutocomplete from '../components/HWAutocomplete.vue'
import type { SearchResult } from '../types/search'

// 响应式数据
const searchValue1 = ref('')
const searchValue2 = ref('')
const searchValue3 = ref('')

const selectedSite1 = ref<SearchResult | null>(null)
const selectedSite2 = ref<SearchResult | null>(null)
const selectedSite3 = ref<SearchResult | null>(null)

const status1 = ref<any>(null)
const status2 = ref<any>(null)
const status3 = ref<any>(null)

const eventLog = ref<Array<{ time: string; message: string }>>([])

// 事件处理
const onSiteSelected1 = (site: any, result: SearchResult) => {
  selectedSite1.value = result
  addLog(`自动补全1选中: ${result.name}`)
}

const onSiteSelected2 = (site: any, result: SearchResult) => {
  selectedSite2.value = result
  addLog(`自动补全2选中: ${result.name}`)
}

const onSiteSelected3 = (site: any, result: SearchResult) => {
  selectedSite3.value = result
  addLog(`自动补全3选中: ${result.name}`)
}

const onStatusChange1 = (status: any) => {
  status1.value = status
  if (status) {
    addLog(`自动补全1状态: ${status.message}`)
  }
}

const onStatusChange2 = (status: any) => {
  status2.value = status
  if (status) {
    addLog(`自动补全2状态: ${status.message}`)
  }
}

const onStatusChange3 = (status: any) => {
  status3.value = status
  if (status) {
    addLog(`自动补全3状态: ${status.message}`)
  }
}

// 控制方法
const clearAll = () => {
  searchValue1.value = ''
  searchValue2.value = ''
  searchValue3.value = ''
  selectedSite1.value = null
  selectedSite2.value = null
  selectedSite3.value = null
  addLog('清空所有输入')
}

const focusFirst = () => {
  // 这里需要通过 ref 访问组件方法
  addLog('聚焦第一个输入框')
}

const testProgrammaticInput = () => {
  searchValue1.value = '天安门'
  searchValue2.value = '故宫'
  searchValue3.value = '北京市东城区'
  addLog('程序化输入测试')
}

const addLog = (message: string) => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  eventLog.value.unshift({ time, message })
  
  // 限制日志数量
  if (eventLog.value.length > 50) {
    eventLog.value = eventLog.value.slice(0, 50)
  }
}

const clearLog = () => {
  eventLog.value = []
}
</script>

<style scoped>
.autocomplete-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.autocomplete-test h1 {
  color: #1890ff;
  margin-bottom: 30px;
  text-align: center;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h2 {
  margin-bottom: 15px;
  color: #333;
  font-size: 18px;
}

.test-item {
  margin-bottom: 20px;
}

.test-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #666;
}

.result-display {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.result-display p {
  margin-bottom: 8px;
  font-size: 14px;
}

.result-display pre {
  background: #f1f3f4;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.button-group button {
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.button-group button:hover {
  background: #40a9ff;
}

.event-log {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 10px;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #666;
  min-width: 80px;
}

.log-message {
  flex: 1;
}
</style>
