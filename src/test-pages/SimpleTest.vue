<template>
  <div class="simple-test">
    <header class="header">
      <h1>华为地图组件简单测试</h1>
      <p>快速验证组件基本功能</p>
    </header>

    <div class="test-container">
      <!-- 基础地图测试 -->
      <section class="test-section">
        <h2>基础地图</h2>
        <div class="map-container">
          <HuaweiMap
            ref="basicMap"
            :center="{ lat: 39.9042, lng: 116.4074 }"
            :zoom="10"
            width="100%"
            height="400px"
            :zoom-control="true"
            :scale-control="true"
            @map-ready="onBasicMapReady"
            @map-error="onMapError"
          />
        </div>
        <div class="controls">
          <button @click="addMarker">添加标记</button>
          <button @click="clearMarkers">清空标记</button>
          <button @click="changeCenter">切换到上海</button>
        </div>
        <div class="status">
          <p>状态: {{ basicMapStatus }}</p>
          <p>标记数量: {{ markerCount }}</p>
        </div>
      </section>

      <!-- 配置测试 -->
      <section class="test-section">
        <h2>配置测试</h2>
        <div class="config-layout">
          <div class="map-container">
            <HuaweiMap
              ref="configMap"
              :center="config.center"
              :zoom="config.zoom"
              :source-type="config.sourceType"
              :map-type="config.mapType"
              :zoom-control="config.controls?.zoom"
              :scale-control="config.controls?.scale"
              width="100%"
              height="450px"
              @map-ready="onConfigMapReady"
            />
          </div>
          <div class="config-panel">
            <h3>地图配置</h3>
            <div class="config-item">
              <label>中心点纬度:</label>
              <input
                v-model.number="config.center.lat"
                type="number"
                step="0.000001"
                @change="updateMapConfig"
              />
            </div>
            <div class="config-item">
              <label>中心点经度:</label>
              <input
                v-model.number="config.center.lng"
                type="number"
                step="0.000001"
                @change="updateMapConfig"
              />
            </div>
            <div class="config-item">
              <label>缩放级别:</label>
              <input
                v-model.number="config.zoom"
                type="number"
                min="1"
                max="20"
                @change="updateMapConfig"
              />
            </div>
            <div class="config-item">
              <label>瓦片类型:</label>
              <select v-model="config.sourceType" @change="updateMapConfig">
                <option value="vector">矢量</option>
                <option value="raster">栅格</option>
              </select>
            </div>
            <div class="config-item">
              <label>地图类型:</label>
              <select v-model="config.mapType" @change="updateMapConfig">
                <option value="ROADMAP">道路图</option>
                <option value="TERRAIN">地形图</option>
              </select>
            </div>
            <div class="config-item">
              <label>
                <input
                  v-model="config.controls.zoom"
                  type="checkbox"
                  @change="updateMapConfig"
                />
                显示缩放控件
              </label>
            </div>
            <div class="config-item">
              <label>
                <input
                  v-model="config.controls.scale"
                  type="checkbox"
                  @change="updateMapConfig"
                />
                显示比例尺
              </label>
            </div>
          </div>
        </div>
      </section>

      <!-- 事件测试 -->
      <section class="test-section">
        <h2>事件测试</h2>
        <div class="map-container">
          <HuaweiMap
            :center="{ lat: 31.2304, lng: 121.4737 }"
            :zoom="11"
            width="100%"
            height="350px"
            :enable-event-monitor="true"
            @map-ready="onEventMapReady"
            @map-click="onMapClick"
            @map-dblclick="onMapDblClick"
            @center-changed="onCenterChanged"
            @zoom-changed="onZoomChanged"
          />
        </div>
        <div class="event-log">
          <h3>事件日志 (最近10条)</h3>
          <div class="log-list">
            <div
              v-for="(event, index) in recentEvents"
              :key="index"
              class="log-item"
            >
              <span class="log-time">{{ event.time }}</span>
              <span class="log-type">{{ event.type }}</span>
              <span class="log-data">{{ event.data }}</span>
            </div>
            <div v-if="recentEvents.length === 0" class="no-events">
              暂无事件记录
            </div>
          </div>
          <button @click="clearEvents" class="clear-btn">清空日志</button>
        </div>
      </section>

      <!-- 功能测试结果 -->
      <section class="test-section">
        <h2>测试结果</h2>
        <div class="test-results">
          <div class="result-item" :class="{ success: testResults.mapLoad, error: testResults.mapLoad === false }">
            <span class="result-label">地图加载:</span>
            <span class="result-value">{{ getResultText(testResults.mapLoad) }}</span>
          </div>
          <div class="result-item" :class="{ success: testResults.markerAdd, error: testResults.markerAdd === false }">
            <span class="result-label">标记添加:</span>
            <span class="result-value">{{ getResultText(testResults.markerAdd) }}</span>
          </div>
          <div class="result-item" :class="{ success: testResults.configChange, error: testResults.configChange === false }">
            <span class="result-label">配置变更:</span>
            <span class="result-value">{{ getResultText(testResults.configChange) }}</span>
          </div>
          <div class="result-item" :class="{ success: testResults.eventCapture, error: testResults.eventCapture === false }">
            <span class="result-label">事件捕获:</span>
            <span class="result-value">{{ getResultText(testResults.eventCapture) }}</span>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import HuaweiMap from '../components/HuaweiMap.vue'

// 地图引用
const basicMap = ref()
const configMap = ref()

// 状态
const basicMapStatus = ref('未加载')
const markerCount = ref(0)

// 配置
const config = reactive({
  center: { lat: 23.1291, lng: 113.2644 },
  zoom: 10,
  sourceType: 'vector' as 'vector' | 'raster',
  mapType: 'ROADMAP' as 'ROADMAP' | 'TERRAIN',
  controls: {
    zoom: true,
    scale: true
  }
})

// 事件日志
const events = ref<Array<{ time: string; type: string; data: string }>>([])
const recentEvents = computed(() => events.value.slice(0, 10))

// 测试结果
const testResults = reactive({
  mapLoad: null as boolean | null,
  markerAdd: null as boolean | null,
  configChange: null as boolean | null,
  eventCapture: null as boolean | null
})

// 事件处理
const onBasicMapReady = (map: any) => {
  console.log('基础地图加载完成:', map)
  basicMapStatus.value = '已加载'
  testResults.mapLoad = true
  addEvent('map-ready', '基础地图初始化完成')
}

const onConfigMapReady = (map: any) => {
  console.log('配置地图加载完成:', map)
  addEvent('config-map-ready', '配置地图初始化完成')
  testResults.configChange = true

  // 确保地图正确显示
  setTimeout(() => {
    if (map && map.resize) {
      map.resize()
    }
  }, 200)

  // 再次确保地图完全渲染
  setTimeout(() => {
    if (map && map.resize) {
      map.resize()
    }
  }, 500)
}

const onEventMapReady = (map: any) => {
  console.log('事件地图加载完成:', map)
  addEvent('event-map-ready', '事件地图初始化完成')
}

const onMapError = (error: string) => {
  console.error('地图加载错误:', error)
  basicMapStatus.value = '加载失败: ' + error
  testResults.mapLoad = false
  addEvent('map-error', error)
}

const onMapClick = (event: any) => {
  const lat = event.latLng?.lat?.toFixed(6) || 'N/A'
  const lng = event.latLng?.lng?.toFixed(6) || 'N/A'
  addEvent('map-click', `点击位置: ${lat}, ${lng}`)
  testResults.eventCapture = true
}

const onMapDblClick = (event: any) => {
  const lat = event.latLng?.lat?.toFixed(6) || 'N/A'
  const lng = event.latLng?.lng?.toFixed(6) || 'N/A'
  addEvent('map-dblclick', `双击位置: ${lat}, ${lng}`)
}

const onCenterChanged = (center: any) => {
  const lat = center.lat?.toFixed(6) || 'N/A'
  const lng = center.lng?.toFixed(6) || 'N/A'
  addEvent('center-changed', `中心点: ${lat}, ${lng}`)
}

const onZoomChanged = (zoom: number) => {
  addEvent('zoom-changed', `缩放级别: ${zoom}`)
}

// 操作方法
const addMarker = () => {
  if (basicMap.value) {
    try {
      const lat = 39.9042 + (Math.random() - 0.5) * 0.1
      const lng = 116.4074 + (Math.random() - 0.5) * 0.1
      const id = basicMap.value.addMarker({
        position: { lat, lng },
        title: `标记 ${markerCount.value + 1}`,
        content: `这是第 ${markerCount.value + 1} 个标记`
      })
      markerCount.value++
      testResults.markerAdd = true
      addEvent('add-marker', `添加标记: ${id}`)
    } catch (error) {
      testResults.markerAdd = false
      addEvent('add-marker-error', `添加标记失败: ${error}`)
    }
  }
}

const clearMarkers = () => {
  if (basicMap.value) {
    try {
      const count = basicMap.value.clearMarkers()
      markerCount.value = 0
      addEvent('clear-markers', `清空了 ${count} 个标记`)
    } catch (error) {
      addEvent('clear-markers-error', `清空标记失败: ${error}`)
    }
  }
}

const changeCenter = () => {
  if (basicMap.value) {
    try {
      // 切换到上海
      const newCenter = { lat: 31.2304, lng: 121.4737 }
      // 这里需要实现地图中心点变更的方法
      addEvent('change-center', '切换到上海')
    } catch (error) {
      addEvent('change-center-error', `切换中心点失败: ${error}`)
    }
  }
}

const updateMapConfig = () => {
  // 配置更新会自动触发地图重新渲染
  testResults.configChange = true
  addEvent('config-update', '配置已更新')
}

const clearEvents = () => {
  events.value = []
}

// 工具方法
const addEvent = (type: string, data: string) => {
  const time = new Date().toLocaleTimeString()
  events.value.unshift({ time, type, data })
  if (events.value.length > 100) {
    events.value = events.value.slice(0, 100)
  }
}

const getResultText = (result: boolean | null) => {
  if (result === null) return '未测试'
  return result ? '通过' : '失败'
}
</script>

<style scoped>
.simple-test {
  min-height: 100vh;
  background: #f8f9fa;
}

.header {
  background: white;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.header h1 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 28px;
}

.header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.test-section {
  background: white;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 8px;
}

.map-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 15px;
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.controls button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
  font-size: 14px;
}

.controls button:hover {
  background: #0056b3;
}

.status {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

.status p {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.config-layout {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 20px;
  min-height: 500px;
}

.config-panel {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.config-panel h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.config-item {
  margin-bottom: 15px;
}

.config-item label {
  display: block;
  margin-bottom: 5px;
  color: #555;
  font-size: 14px;
  font-weight: 500;
}

.config-item input,
.config-item select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.config-item input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

.event-log {
  margin-top: 15px;
}

.event-log h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.log-list {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 10px;
}

.log-item {
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  gap: 10px;
  font-size: 12px;
  font-family: monospace;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #666;
  min-width: 80px;
}

.log-type {
  color: #007bff;
  min-width: 120px;
  font-weight: 500;
}

.log-data {
  color: #333;
  flex: 1;
}

.no-events {
  padding: 20px;
  text-align: center;
  color: #999;
  font-style: italic;
}

.clear-btn {
  padding: 6px 12px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-btn:hover {
  background: #545b62;
}

.test-results {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.result-item {
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  background: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-item.success {
  background: #d4edda;
  border-color: #c3e6cb;
}

.result-item.error {
  background: #f8d7da;
  border-color: #f5c6cb;
}

.result-label {
  font-weight: 500;
  color: #333;
}

.result-value {
  font-weight: bold;
}

.result-item.success .result-value {
  color: #155724;
}

.result-item.error .result-value {
  color: #721c24;
}

@media (max-width: 768px) {
  .config-layout {
    grid-template-columns: 1fr;
  }

  .test-container {
    padding: 0 15px;
  }

  .test-section {
    padding: 20px 15px;
  }

  .controls {
    justify-content: center;
  }

  .test-results {
    grid-template-columns: 1fr;
  }
}
</style>
