<template>
  <div class="routing-test-simple">
    <div class="header">
      <h1>路径规划 + 自动补全测试</h1>
      <p>测试华为地图路径规划功能与 HWAutocomplete 的集成</p>
    </div>

    <div class="main-content">
      <!-- 左侧控制面板 -->
      <div class="control-panel">
        <RoutingPanel
          :map="map"
          :map-center="mapCenter"
          @route-planned="onRoutePlanned"
          @route-displayed="onRouteDisplayed"
          @route-cleared="onRouteCleared"
          @waypoint-selected="onWaypointSelected"
        />

        <!-- 快速测试 -->
        <div class="quick-actions">
          <h3>快速测试</h3>
          <button @click="testBeijingRoute" class="action-btn">
            🏛️ 北京路线
          </button>
          <button @click="testShanghaiRoute" class="action-btn">
            🏙️ 上海路线
          </button>
          <button @click="clearAll" class="action-btn danger">
            🗑️ 清除
          </button>
        </div>
      </div>

      <!-- 右侧地图 -->
      <div class="map-panel">
        <HuaweiMap
          ref="mapRef"
          width="100%"
          height="100%"
          :center="mapCenter"
          :zoom="mapZoom"
          @map-ready="onMapReady"
          @map-click="onMapClick"
        />

        <!-- 地图状态 -->
        <div class="map-info">
          <span>{{ mapCenter.lat.toFixed(4) }}, {{ mapCenter.lng.toFixed(4) }}</span>
          <span>缩放: {{ mapZoom }}</span>
        </div>
      </div>
    </div>

    <!-- 底部日志 -->
    <div class="log-panel">
      <div class="log-header">
        <span>操作日志</span>
        <button @click="clearLog" class="clear-btn">清除</button>
      </div>
      <div class="log-content">
        <div
          v-for="(event, index) in eventLog.slice(0, 5)"
          :key="index"
          class="log-item"
          :class="event.type"
        >
          <span class="log-time">{{ event.time }}</span>
          <span class="log-message">{{ event.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import HuaweiMap from '../components/HuaweiMap.vue'
import RoutingPanel from '../components/RoutingPanel.vue'
import { useMapMarkers } from '../composables/useMapMarkers'
import { useRouting } from '../composables/useRouting'
import type { LatLng } from '../types/routing'

// 地图相关
const mapRef = ref()
const map = ref<any>(null)
const mapCenter = ref<LatLng>({ lat: 39.9042, lng: 116.4074 })
const mapZoom = ref(12)

// 标记管理
const { markers, addMarker, clearMarkers } = useMapMarkers(map)

// 路径规划
const { clearAllRoutes: clearRoutingRoutes } = useRouting(map)

// 事件日志
const eventLog = ref<Array<{ time: string; message: string; type: string }>>([])

// 预设路线
const presetRoutes = {
  beijing: {
    origin: { name: '天安门广场', lat: 39.9042, lng: 116.4074 },
    destination: { name: '故宫博物院', lat: 39.9163, lng: 116.3972 },
    waypoints: [{ name: '王府井大街', lat: 39.9097, lng: 116.4180 }]
  },
  shanghai: {
    origin: { name: '外滩', lat: 31.2304, lng: 121.4737 },
    destination: { name: '陆家嘴', lat: 31.2397, lng: 121.4994 },
    waypoints: [{ name: '南京路步行街', lat: 31.2342, lng: 121.4692 }]
  }
}

// 事件处理
const onMapReady = (mapInstance: any) => {
  map.value = mapInstance
  addEvent('success', '地图加载完成')
}

const onMapClick = (event: any) => {
  const { lat, lng } = event.latLng
  addEvent('info', `地图点击: ${lat.toFixed(4)}, ${lng.toFixed(4)}`)
}

const onRoutePlanned = (route: any) => {
  addEvent('success', `路径规划完成`)
}

const onRouteDisplayed = (route: any) => {
  addEvent('info', '路径已显示')
}

const onRouteCleared = () => {
  addEvent('info', '路径已清除')
}

const onWaypointSelected = (type: string) => {
  addEvent('info', `请选择${type === 'origin' ? '起点' : '终点'}`)
}

// 快速测试
const testBeijingRoute = () => {
  setPresetRoute('beijing')
  addEvent('info', '设置北京测试路线')
}

const testShanghaiRoute = () => {
  setPresetRoute('shanghai')
  addEvent('info', '设置上海测试路线')
}

const setPresetRoute = (routeKey: keyof typeof presetRoutes) => {
  const route = presetRoutes[routeKey]
  mapCenter.value = { lat: route.origin.lat, lng: route.origin.lng }
  mapZoom.value = 13

  // 添加标记
  addMarker({
    id: 'preset-origin',
    position: { lat: route.origin.lat, lng: route.origin.lng },
    title: route.origin.name,
    content: `<div><h4>起点</h4><p>${route.origin.name}</p></div>`
  })

  addMarker({
    id: 'preset-destination',
    position: { lat: route.destination.lat, lng: route.destination.lng },
    title: route.destination.name,
    content: `<div><h4>终点</h4><p>${route.destination.name}</p></div>`
  })
}

const clearAll = () => {
  clearMarkers()
  clearRoutingRoutes()
  addEvent('info', '已清除所有内容')
}

// 工具方法
const addEvent = (type: string, message: string) => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  eventLog.value.unshift({ time, message, type })

  if (eventLog.value.length > 20) {
    eventLog.value = eventLog.value.slice(0, 20)
  }
}

const clearLog = () => {
  eventLog.value = []
}
</script>

<style scoped>
.routing-test-simple {
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f5f5f5;
}

.header {
  background: white;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
  text-align: center;
}

.header h1 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #1890ff;
}

.header p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.main-content {
  flex: 1;
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 0;
  min-height: 0;
}

.control-panel {
  background: white;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.quick-actions {
  padding: 15px;
  border-top: 1px solid #f0f0f0;
}

.quick-actions h3 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #333;
}

.action-btn {
  display: block;
  width: 100%;
  margin-bottom: 8px;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  text-align: left;
  transition: background 0.3s ease;
}

.action-btn:not(.danger) {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.action-btn:not(.danger):hover {
  background: #bae7ff;
}

.action-btn.danger {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.action-btn.danger:hover {
  background: #ffccc7;
}

.map-panel {
  position: relative;
  background: #f0f0f0;
}

.map-info {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  gap: 15px;
}

.log-panel {
  background: white;
  border-top: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
  font-weight: 500;
}

.clear-btn {
  padding: 2px 8px;
  background: none;
  border: 1px solid #d0d0d0;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
}

.clear-btn:hover {
  background: #f0f0f0;
}

.log-content {
  height: 80px;
  overflow-y: auto;
  padding: 5px 15px;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 2px 0;
  font-size: 11px;
}

.log-item.success {
  color: #52c41a;
}

.log-item.error {
  color: #ff4d4f;
}

.log-item.info {
  color: #1890ff;
}

.log-time {
  color: #999;
  min-width: 70px;
}

.log-message {
  flex: 1;
}

@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }

  .control-panel {
    max-height: 300px;
  }
}
</style>
