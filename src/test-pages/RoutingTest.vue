<template>
  <div class="routing-test">
    <h1>华为地图路径规划测试 (v1.1.0)</h1>
    <p class="subtitle">测试路径规划功能和 HWAutocomplete 集成</p>

    <div class="test-layout">
      <!-- 左侧路径规划面板 -->
      <div class="routing-container">
        <RoutingPanel
          :map="map"
          :map-center="mapCenter"
          @route-planned="onRoutePlanned"
          @route-displayed="onRouteDisplayed"
          @route-cleared="onRouteCleared"
          @waypoint-selected="onWaypointSelected"
        />

        <!-- 快速测试按钮 -->
        <div class="quick-test-panel">
          <h3>快速测试</h3>
          <div class="test-buttons">
            <button @click="testBeijingRoute" class="test-btn">
              🏛️ 北京景点路线
            </button>
            <button @click="testShanghaRoute" class="test-btn">
              🏙️ 上海商务路线
            </button>
            <button @click="testGuangzhouRoute" class="test-btn">
              🌸 广州美食路线
            </button>
            <button @click="clearAllRoutes" class="test-btn clear">
              🗑️ 清除所有
            </button>
          </div>
        </div>

        <!-- 功能说明 -->
        <div class="feature-info">
          <h3>功能特性</h3>
          <ul class="feature-list">
            <li>✅ 起点/终点自动补全</li>
            <li>✅ 途经点智能搜索</li>
            <li>✅ 多种路径类型支持</li>
            <li>✅ 实时路径规划</li>
            <li>✅ 路径详情展示</li>
            <li>✅ 历史记录管理</li>
          </ul>
        </div>
      </div>

      <!-- 右侧地图区域 -->
      <div class="map-container">
        <HuaweiMap
          ref="mapRef"
          width="100%"
          height="100%"
          :center="mapCenter"
          :zoom="mapZoom"
          @map-ready="onMapReady"
          @map-click="onMapClick"
        />

        <!-- 地图控制按钮 -->
        <div class="map-controls">
          <button @click="resetMapView" class="control-btn">
            🎯 重置视图
          </button>
          <button @click="toggleMapType" class="control-btn">
            🗺️ 切换地图
          </button>
          <button @click="centerToBeijing" class="control-btn">
            📍 定位北京
          </button>
        </div>

        <!-- 地图状态显示 -->
        <div class="map-status">
          <div class="status-item">
            <span class="status-label">中心位置:</span>
            <span class="status-value">{{ mapCenter.lat.toFixed(4) }}, {{ mapCenter.lng.toFixed(4) }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">缩放级别:</span>
            <span class="status-value">{{ mapZoom }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">当前模式:</span>
            <span class="status-value">{{ currentMode }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 事件日志 -->
    <div class="event-log">
      <div class="log-header">
        <h3>路径规划日志</h3>
        <button @click="clearLog" class="clear-log-btn">清除日志</button>
      </div>
      <div class="log-content">
        <div
          v-for="(event, index) in eventLog"
          :key="index"
          class="log-item"
          :class="event.type"
        >
          <span class="log-time">{{ event.time }}</span>
          <span class="log-message">{{ event.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import HuaweiMap from '../components/HuaweiMap.vue'
import RoutingPanel from '../components/RoutingPanel.vue'
import { useMapMarkers } from '../composables/useMapMarkers'
import { useRouting } from '../composables/useRouting'
import type { LatLng } from '../types/routing'

// 地图相关
const mapRef = ref()
const map = ref<any>(null)
const mapCenter = ref<LatLng>({ lat: 39.9042, lng: 116.4074 })
const mapZoom = ref(12)
const currentMode = ref('路径规划')

// 标记管理
const { markers, addMarker, clearMarkers } = useMapMarkers(map)

// 路径规划
const { clearAllRoutes } = useRouting()

// 事件日志
const eventLog = ref<Array<{ time: string; message: string; type: string }>>([])

// 预设路线数据
const presetRoutes = {
  beijing: {
    origin: { name: '天安门广场', lat: 39.9042, lng: 116.4074 },
    destination: { name: '故宫博物院', lat: 39.9163, lng: 116.3972 },
    waypoints: [
      { name: '王府井大街', lat: 39.9097, lng: 116.4180 }
    ]
  },
  shanghai: {
    origin: { name: '外滩', lat: 31.2304, lng: 121.4737 },
    destination: { name: '陆家嘴', lat: 31.2397, lng: 121.4994 },
    waypoints: [
      { name: '南京路步行街', lat: 31.2342, lng: 121.4692 }
    ]
  },
  guangzhou: {
    origin: { name: '广州塔', lat: 23.1081, lng: 113.3245 },
    destination: { name: '沙面岛', lat: 23.1067, lng: 113.2367 },
    waypoints: [
      { name: '上下九步行街', lat: 23.1167, lng: 113.2500 }
    ]
  }
}

// 事件处理
const onMapReady = (mapInstance: any) => {
  map.value = mapInstance
  addEvent('success', '地图初始化完成，可以开始路径规划测试')
}

const onMapClick = (event: any) => {
  const { lat, lng } = event.latLng
  addEvent('info', `地图点击: ${lat.toFixed(4)}, ${lng.toFixed(4)}`)
}

const onRoutePlanned = (route: any) => {
  addEvent('success', `路径规划完成: ${route.summary || '路径已生成'}`)
  if (route.totalDistance && route.totalDuration) {
    addEvent('info', `距离: ${formatDistance(route.totalDistance)}, 时间: ${formatDuration(route.totalDuration)}`)
  }
}

const onRouteDisplayed = (route: any) => {
  addEvent('info', '路径已在地图上显示')
}

const onRouteCleared = () => {
  addEvent('info', '路径已清除')
}

const onWaypointSelected = (type: string) => {
  currentMode.value = type === 'origin' ? '选择起点' : '选择终点'
  addEvent('info', `请在地图上点击选择${type === 'origin' ? '起点' : '终点'}`)
}

// 快速测试方法
const testBeijingRoute = () => {
  setPresetRoute('beijing')
  addEvent('info', '已设置北京景点路线测试数据')
}

const testShanghaRoute = () => {
  setPresetRoute('shanghai')
  addEvent('info', '已设置上海商务路线测试数据')
}

const testGuangzhouRoute = () => {
  setPresetRoute('guangzhou')
  addEvent('info', '已设置广州美食路线测试数据')
}

const setPresetRoute = (routeKey: keyof typeof presetRoutes) => {
  const route = presetRoutes[routeKey]
  
  // 设置地图中心到起点
  mapCenter.value = { lat: route.origin.lat, lng: route.origin.lng }
  mapZoom.value = 13
  
  // 添加标记
  addMarker({
    id: 'preset-origin',
    position: { lat: route.origin.lat, lng: route.origin.lng },
    title: route.origin.name,
    content: `<div><h4>起点</h4><p>${route.origin.name}</p></div>`
  })
  
  addMarker({
    id: 'preset-destination',
    position: { lat: route.destination.lat, lng: route.destination.lng },
    title: route.destination.name,
    content: `<div><h4>终点</h4><p>${route.destination.name}</p></div>`
  })
  
  route.waypoints.forEach((waypoint, index) => {
    addMarker({
      id: `preset-waypoint-${index}`,
      position: { lat: waypoint.lat, lng: waypoint.lng },
      title: waypoint.name,
      content: `<div><h4>途经点 ${index + 1}</h4><p>${waypoint.name}</p></div>`
    })
  })
}

// 地图控制方法
const resetMapView = () => {
  mapCenter.value = { lat: 39.9042, lng: 116.4074 }
  mapZoom.value = 12
  addEvent('info', '地图视图已重置')
}

const toggleMapType = () => {
  // 这里可以实现地图类型切换
  addEvent('info', '地图类型切换功能待实现')
}

const centerToBeijing = () => {
  mapCenter.value = { lat: 39.9042, lng: 116.4074 }
  mapZoom.value = 12
  addEvent('info', '地图已定位到北京')
}

const clearAllRoutes = () => {
  clearMarkers()
  clearAllRoutes()
  addEvent('info', '所有路径和标记已清除')
}

// 工具方法
const addEvent = (type: string, message: string) => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  eventLog.value.unshift({ time, message, type })
  
  // 限制日志数量
  if (eventLog.value.length > 50) {
    eventLog.value = eventLog.value.slice(0, 50)
  }
}

const clearLog = () => {
  eventLog.value = []
}

const formatDistance = (distance: number): string => {
  if (distance < 1000) {
    return `${Math.round(distance)}米`
  } else {
    return `${(distance / 1000).toFixed(1)}公里`
  }
}

const formatDuration = (duration: number): string => {
  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}
</script>

<style scoped>
.routing-test {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f5f5f5;
  min-height: 100vh;
}

.routing-test h1 {
  margin-bottom: 8px;
  color: #1890ff;
  text-align: center;
  font-size: 28px;
}

.subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
  font-size: 16px;
}

.test-layout {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 20px;
  height: 700px;
  margin-bottom: 20px;
}

.routing-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.quick-test-panel {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-test-panel h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #333;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-btn {
  padding: 10px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  text-align: left;
  transition: background 0.3s ease;
}

.test-btn:not(.clear) {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.test-btn:not(.clear):hover {
  background: #bae7ff;
}

.test-btn.clear {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.test-btn.clear:hover {
  background: #ffccc7;
}

.feature-info {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feature-info h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #333;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  padding: 4px 0;
  font-size: 14px;
  color: #666;
}

.map-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-btn {
  padding: 8px 12px;
  background: white;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.control-btn:hover {
  background: #f0f0f0;
}

.map-status {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(255, 255, 255, 0.95);
  padding: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  min-width: 200px;
}

.status-label {
  color: #666;
}

.status-value {
  font-weight: 500;
}

.event-log {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.log-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.clear-log-btn {
  padding: 4px 8px;
  background: none;
  border: 1px solid #d0d0d0;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.clear-log-btn:hover {
  background: #f0f0f0;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 4px 8px;
  border-radius: 3px;
  margin-bottom: 2px;
  font-size: 12px;
}

.log-item.success {
  background: #f6ffed;
  color: #52c41a;
}

.log-item.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.log-item.info {
  background: #f0f9ff;
  color: #1890ff;
}

.log-time {
  color: #666;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

@media (max-width: 1200px) {
  .test-layout {
    grid-template-columns: 1fr;
    height: auto;
  }
  
  .routing-container {
    order: 2;
  }
  
  .map-container {
    order: 1;
    height: 500px;
  }
}
</style>
