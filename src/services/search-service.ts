// 华为地图搜索服务实现

import { defaultMapConfig } from '../config/map-config';
import type {
  LatLng,
  SearchOptions,
  SearchResult,
  GeocodingResult,
  ReverseGeocodingResult,
  POIResult,
  SuggestionResult,
  SearchService,
  AddressComponents
} from '../types/search';

/**
 * 华为地图搜索服务
 */
export class HuaweiSearchService implements SearchService {
  private apiKey: string;
  private siteService: any;
  private autocompleteInstances: Map<string, any> = new Map();

  constructor(apiKey?: string) {
    this.apiKey = apiKey || defaultMapConfig.apiKey;
    this.initSiteService();
  }

  /**
   * 初始化华为地图站点服务
   */
  private initSiteService() {
    if (typeof window !== 'undefined' && (window as any).HWMapJsSDK) {
      this.siteService = new (window as any).HWMapJsSDK.HWSiteService();
    }
  }

  /**
   * 创建自动补全实例
   */
  createAutocomplete(inputElement: HTMLInputElement, options: any = {}): any {
    if (!window.HWMapJsSDK) {
      console.error('华为地图SDK未加载');
      return null;
    }

    const autocompleteOptions = {
      language: 'zh-CN',
      maxHeight: '200px',
      ...options
    };

    const autocomplete = new (window as any).HWMapJsSDK.HWAutocomplete(inputElement, autocompleteOptions);

    // 存储实例以便后续管理
    const instanceId = Math.random().toString(36);
    this.autocompleteInstances.set(instanceId, autocomplete);

    return {
      instance: autocomplete,
      instanceId,
      addListener: (eventName: string, callback: Function) => {
        autocomplete.addListener(eventName, callback);
      },
      getSite: () => autocomplete.getSite(),
      destroy: () => {
        this.autocompleteInstances.delete(instanceId);
      }
    };
  }

  /**
   * 地点搜索
   */
  async searchPlace(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {
    return new Promise((resolve, reject) => {
      if (!this.siteService) {
        reject(new Error('华为地图SDK未加载'));
        return;
      }

      const request: any = {
        query: query,
        language: 'zh-CN',
        pageSize: Math.min(options.limit || 20, 20),
        pageIndex: 1
      };

      if (options.center) {
        request.location = {
          lat: options.center.lat,
          lng: options.center.lng
        };
      }

      if (options.radius) {
        request.radius = options.radius;
      }

      if (options.city) {
        request.countryCode = 'CN'; // 限制在中国范围内搜索
      }

      if (options.types && options.types.length > 0) {
        request.poiType = options.types.join('|');
      }

      this.siteService.searchByText(request, (result: any, status: any) => {
        // 华为地图API成功状态码可能是 '0' 或 'OK'
        if ((status === '0' || status === 'OK') && result && result.sites) {
          resolve(this.parseSearchResults(result.sites));
        } else {
          reject(new Error(`搜索失败: ${status}`));
        }
      });
    });
  }

  /**
   * 地理编码 - 地址转坐标
   */
  async geocode(address: string): Promise<GeocodingResult> {
    return new Promise((resolve, reject) => {
      if (!this.siteService) {
        reject(new Error('华为地图SDK未加载'));
        return;
      }

      const request = {
        address: address,
        language: 'zh-CN'
      };

      this.siteService.geocode(request, (result: any, status: any) => {
        if ((status === '0' || status === 'OK') && result && result.sites && result.sites.length > 0) {
          const site = result.sites[0];
          resolve({
            address: address,
            location: {
              lat: parseFloat(site.location.lat),
              lng: parseFloat(site.location.lng)
            },
            confidence: site.confidence || 1.0,
            components: this.parseAddressComponents(site)
          });
        } else {
          reject(new Error(`地理编码失败: ${status}`));
        }
      });
    });
  }

  /**
   * 逆地理编码 - 坐标转地址
   */
  async reverseGeocode(lat: number, lng: number): Promise<ReverseGeocodingResult> {
    return new Promise((resolve, reject) => {
      if (!this.siteService) {
        reject(new Error('华为地图SDK未加载'));
        return;
      }

      const request = {
        location: { lat, lng },
        language: 'zh-CN',
        returnPoi: true
      };

      this.siteService.reverseGeocode(request, (result: any, status: any) => {
        if ((status === '0' || status === 'OK') && result && result.sites && result.sites.length > 0) {
          const site = result.sites[0];
          resolve({
            location: { lat, lng },
            formattedAddress: site.formatAddress || '',
            components: this.parseAddressComponents(site),
            nearbyPOIs: this.parsePOIResults(result.pois || [])
          });
        } else {
          reject(new Error(`逆地理编码失败: ${status}`));
        }
      });
    });
  }

  /**
   * 周边搜索
   */
  async searchNearby(center: LatLng, radius: number, keyword?: string): Promise<POIResult[]> {
    return new Promise((resolve, reject) => {
      if (!this.siteService) {
        reject(new Error('华为地图SDK未加载'));
        return;
      }

      const request: any = {
        location: { lat: center.lat, lng: center.lng },
        radius: radius,
        language: 'zh-CN',
        pageSize: 20,
        pageIndex: 1
      };

      if (keyword) {
        request.query = keyword;
      }

      this.siteService.nearbySearch(request, (result: any, status: any) => {
        if ((status === '0' || status === 'OK') && result && result.sites) {
          resolve(this.parsePOIResults(result.sites));
        } else {
          reject(new Error(`周边搜索失败: ${status}`));
        }
      });
    });
  }

  /**
   * 搜索建议
   */
  async getSuggestions(query: string): Promise<SuggestionResult[]> {
    return new Promise((resolve, reject) => {
      if (!this.siteService) {
        reject(new Error('华为地图SDK未加载'));
        return;
      }

      const request = {
        query: query,
        language: 'zh-CN'
      };

      this.siteService.querySuggestion(request, (result: any, status: any) => {
        if ((status === '0' || status === 'OK') && result && result.sites) {
          resolve(this.parseSuggestionResults(result.sites));
        } else {
          reject(new Error(`搜索建议失败: ${status}`));
        }
      });
    });
  }

  /**
   * 解析搜索结果
   */
  private parseSearchResults(sites: any[]): SearchResult[] {
    return sites.map(site => ({
      id: site.siteId || site.id || Math.random().toString(36),
      name: site.name || '',
      address: site.formatAddress || site.address || '',
      location: {
        lat: parseFloat(site.location.lat),
        lng: parseFloat(site.location.lng)
      },
      type: site.poi?.poiType || 'unknown',
      distance: site.distance ? parseFloat(site.distance) : undefined,
      rating: site.poi?.rating ? parseFloat(site.poi.rating) : undefined,
      phone: site.poi?.phone || undefined,
      details: site.poi || {}
    }));
  }

  /**
   * 解析POI结果
   */
  private parsePOIResults(sites: any[]): POIResult[] {
    return sites.map(site => ({
      id: site.siteId || site.id || Math.random().toString(36),
      name: site.name || '',
      type: site.poi?.poiType || 'unknown',
      location: {
        lat: parseFloat(site.location.lat),
        lng: parseFloat(site.location.lng)
      },
      address: site.formatAddress || site.address || '',
      distance: site.distance ? parseFloat(site.distance) : 0,
      tags: site.poi?.tags || []
    }));
  }

  /**
   * 解析搜索建议结果
   */
  private parseSuggestionResults(sites: any[]): SuggestionResult[] {
    return sites.map(site => ({
      text: site.name || site.formatAddress || '',
      type: site.poi ? 'poi' : 'address',
      location: site.location ? {
        lat: parseFloat(site.location.lat),
        lng: parseFloat(site.location.lng)
      } : undefined,
      address: site.formatAddress || undefined
    }));
  }

  /**
   * 解析地址组件
   */
  private parseAddressComponents(site: any): AddressComponents {
    const addressDetail = site.addressDetail || {};
    return {
      country: addressDetail.country || '中国',
      province: addressDetail.province || '',
      city: addressDetail.city || '',
      district: addressDetail.district || '',
      street: addressDetail.street || '',
      streetNumber: addressDetail.streetNumber || '',
      postalCode: addressDetail.postalCode || ''
    };
  }
}
