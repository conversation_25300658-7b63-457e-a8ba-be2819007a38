// 华为地图路径规划服务实现

import { defaultMapConfig } from '../config/map-config';
import type {
  LatLng,
  RouteType,
  DrivingOptions,
  TransitOptions,
  RouteOptions,
  RouteResult,
  TransitRouteResult,
  RouteDisplayOptions,
  RoutingService,
  RouteLeg,
  RouteStep,
  TransitDetail,
  TransitStop
} from '../types/routing';

/**
 * 华为地图路径规划服务
 */
export class HuaweiRoutingService implements RoutingService {
  private apiKey: string;
  private map: any;
  private routePolylines: any[] = [];
  private routeMarkers: any[] = [];
  private directionsService: any;
  private directionsRenderer: any;

  constructor(apiKey?: string, map?: any) {
    this.apiKey = apiKey || defaultMapConfig.apiKey;
    this.map = map;
    this.initDirectionsService();
  }

  /**
   * 初始化华为地图路径规划服务
   */
  private initDirectionsService() {
    if (typeof window !== 'undefined' && (window as any).HWMapJsSDK) {
      this.directionsService = new (window as any).HWMapJsSDK.HWDirectionsService();
      this.directionsRenderer = new (window as any).HWMapJsSDK.HWDirectionsRenderer();
      if (this.map) {
        this.directionsRenderer.setMap(this.map);
      }
    }
  }

  /**
   * 设置地图实例
   */
  setMap(map: any): void {
    this.map = map;
    if (this.directionsRenderer) {
      this.directionsRenderer.setMap(map);
    }
  }

  /**
   * 驾车路径规划
   */
  async planDrivingRoute(
    origin: LatLng,
    destination: LatLng,
    options: DrivingOptions = {}
  ): Promise<RouteResult> {
    return new Promise((resolve, reject) => {
      if (!this.directionsService) {
        reject(new Error('华为地图SDK未加载'));
        return;
      }

      const request: any = {
        origin: { lat: origin.lat, lng: origin.lng },
        destination: { lat: destination.lat, lng: destination.lng }
      };

      // 添加驾车选项
      if (options.waypoints && options.waypoints.length > 0) {
        request.waypoints = options.waypoints.map(wp => ({ lat: wp.lat, lng: wp.lng }));
      }

      if (options.strategy) {
        const avoidMap = {
          avoid_highway: [2], // 避开高速公路
          avoid_traffic: [1]  // 避免收费的公路
        };
        if (avoidMap[options.strategy]) {
          request.avoid = avoidMap[options.strategy];
        }
      }

      this.directionsService.routeDriving(request, (result: any, status: any) => {
        if ((status === '0' || status === 'OK') && result && result.routes && result.routes.length > 0) {
          resolve(this.parseRouteResult(result.routes[0], 'driving'));
        } else {
          reject(new Error(`驾车路径规划失败: ${status}`));
        }
      });
    });
  }

  /**
   * 步行路径规划
   */
  async planWalkingRoute(origin: LatLng, destination: LatLng): Promise<RouteResult> {
    return new Promise((resolve, reject) => {
      if (!this.directionsService) {
        reject(new Error('华为地图SDK未加载'));
        return;
      }

      const request = {
        origin: { lat: origin.lat, lng: origin.lng },
        destination: { lat: destination.lat, lng: destination.lng }
      };

      this.directionsService.routeWalking(request, (result: any, status: any) => {
        if ((status === '0' || status === 'OK') && result && result.routes && result.routes.length > 0) {
          resolve(this.parseRouteResult(result.routes[0], 'walking'));
        } else {
          reject(new Error(`步行路径规划失败: ${status}`));
        }
      });
    });
  }

  /**
   * 骑行路径规划
   */
  async planBicyclingRoute(origin: LatLng, destination: LatLng): Promise<RouteResult> {
    return new Promise((resolve, reject) => {
      if (!this.directionsService) {
        reject(new Error('华为地图SDK未加载'));
        return;
      }

      const request = {
        origin: { lat: origin.lat, lng: origin.lng },
        destination: { lat: destination.lat, lng: destination.lng }
      };

      this.directionsService.routeBicycling(request, (result: any, status: any) => {
        if ((status === '0' || status === 'OK') && result && result.routes && result.routes.length > 0) {
          resolve(this.parseRouteResult(result.routes[0], 'bicycling'));
        } else {
          reject(new Error(`骑行路径规划失败: ${status}`));
        }
      });
    });
  }

  /**
   * 公交路径规划
   * 注意：华为地图API暂不支持公交路径规划，此方法为占位符
   */
  async planTransitRoute(
    origin: LatLng,
    destination: LatLng,
    options: TransitOptions = {}
  ): Promise<TransitRouteResult> {
    return Promise.reject(new Error('华为地图API暂不支持公交路径规划'));
  }

  /**
   * 多点路径规划
   */
  async planMultiPointRoute(waypoints: LatLng[], options: RouteOptions = { type: 'driving' }): Promise<RouteResult> {
    if (waypoints.length < 2) {
      throw new Error('至少需要2个路径点');
    }

    const origin = waypoints[0];
    const destination = waypoints[waypoints.length - 1];
    const intermediateWaypoints = waypoints.slice(1, -1);

    switch (options.type) {
      case 'driving':
        return this.planDrivingRoute(origin, destination, {
          ...options.drivingOptions,
          waypoints: intermediateWaypoints
        });
      case 'walking':
        return this.planWalkingRoute(origin, destination);
      case 'bicycling':
        return this.planBicyclingRoute(origin, destination);
      case 'transit':
        return this.planTransitRoute(origin, destination, options.transitOptions);
      default:
        throw new Error(`不支持的路径类型: ${options.type}`);
    }
  }

  /**
   * 在地图上显示路径
   */
  displayRoute(route?: RouteResult, options: RouteDisplayOptions = {}): void {
    if (!this.map || !this.directionsRenderer) {
      console.warn('地图实例或路径渲染器未设置，无法显示路径');
      return;
    }

    // 设置渲染选项
    const renderOptions = {
      strokeColor: options.strokeColor || '#1890ff',
      strokeWeight: options.strokeWeight || 6,
      visible: true,
      zIndex: 1000
    };

    this.directionsRenderer.setOptions(renderOptions);

    // 如果提供了路径结果，直接显示
    if (route) {
      // 将我们的RouteResult转换为华为地图的DirectionsResult格式
      const directionsResult = this.convertToDirectionsResult(route);
      this.directionsRenderer.setDirections(directionsResult);
    }
  }
  /**
   * 清除路径显示
   */
  clearRoute(): void {
    if (this.directionsRenderer) {
      this.directionsRenderer.setDirections(null);
    }
  }

  /**
   * 清除所有路径
   */
  clearAllRoutes(): void {
    this.clearRoute();
  }

  /**
   * 将RouteResult转换为华为地图DirectionsResult格式
   */
  private convertToDirectionsResult(route: RouteResult): any {
    return {
      routes: [{
        legs: route.legs.map(leg => ({
          start_location: leg.startLocation,
          end_location: leg.endLocation,
          start_address: leg.startAddress,
          end_address: leg.endAddress,
          distance: { value: leg.distance, text: `${(leg.distance / 1000).toFixed(1)}公里` },
          duration: { value: leg.duration, text: `${Math.round(leg.duration / 60)}分钟` },
          steps: leg.steps.map(step => ({
            distance: { value: step.distance, text: `${step.distance}米` },
            duration: { value: step.duration, text: `${Math.round(step.duration / 60)}分钟` },
            start_location: step.polyline[0],
            end_location: step.polyline[step.polyline.length - 1],
            html_instructions: step.instruction,
            polyline: { points: this.encodePolyline(step.polyline) },
            maneuver: step.maneuver
          }))
        })),
        overview_polyline: { points: this.encodePolyline(route.polyline) },
        bounds: route.bounds,
        summary: route.summary
      }]
    };
  }

  /**
   * 解析路径结果
   */
  private parseRouteResult(route: any, type: RouteType): RouteResult {
    if (!route) {
      throw new Error('无效的路径数据');
    }

    const legs = this.parseRouteLegs(route.legs || []);
    const polyline = this.decodePolyline(route.overview_polyline?.points || '');

    return {
      id: route.id || Math.random().toString(36),
      type,
      totalDistance: route.distance?.value || 0,
      totalDuration: route.duration?.value || 0,
      legs,
      polyline,
      bounds: {
        northeast: route.bounds?.northeast || polyline[0],
        southwest: route.bounds?.southwest || polyline[0]
      },
      summary: route.summary || '',
      fare: route.fare ? {
        currency: route.fare.currency || 'CNY',
        value: route.fare.value || 0
      } : undefined
    };
  }

  /**
   * 解析路径段
   */
  private parseRouteLegs(legs: any[]): RouteLeg[] {
    return legs.map(leg => ({
      startLocation: {
        lat: leg.start_location?.lat || 0,
        lng: leg.start_location?.lng || 0
      },
      endLocation: {
        lat: leg.end_location?.lat || 0,
        lng: leg.end_location?.lng || 0
      },
      startAddress: leg.start_address || '',
      endAddress: leg.end_address || '',
      distance: leg.distance?.value || 0,
      duration: leg.duration?.value || 0,
      steps: this.parseRouteSteps(leg.steps || [])
    }));
  }

  /**
   * 解析路径步骤
   */
  private parseRouteSteps(steps: any[]): RouteStep[] {
    return steps.map(step => ({
      instruction: step.html_instructions || step.instructions || '',
      distance: step.distance?.value || 0,
      duration: step.duration?.value || 0,
      polyline: this.decodePolyline(step.polyline?.points || ''),
      maneuver: step.maneuver || '',
      roadName: step.road_name || ''
    }));
  }

  /**
   * 解析公交详情
   */
  private parseTransitDetails(legs: any[]): TransitDetail[] {
    const details: TransitDetail[] = [];

    legs.forEach(leg => {
      leg.steps?.forEach((step: any) => {
        if (step.travel_mode === 'TRANSIT' && step.transit_details) {
          const transit = step.transit_details;
          details.push({
            lineName: transit.line?.name || '',
            mode: this.mapTransitMode(transit.line?.vehicle?.type),
            departureStop: this.parseTransitStop(transit.departure_stop),
            arrivalStop: this.parseTransitStop(transit.arrival_stop),
            numStops: transit.num_stops || 0,
            color: transit.line?.color || undefined
          });
        }
      });
    });

    return details;
  }

  /**
   * 解析公交站点
   */
  private parseTransitStop(stop: any): TransitStop {
    return {
      name: stop?.name || '',
      location: {
        lat: stop?.location?.lat || 0,
        lng: stop?.location?.lng || 0
      },
      arrivalTime: stop?.arrival_time?.value ? new Date(stop.arrival_time.value * 1000) : undefined,
      departureTime: stop?.departure_time?.value ? new Date(stop.departure_time.value * 1000) : undefined
    };
  }

  /**
   * 映射公交方式
   */
  private mapTransitMode(vehicleType: string): 'bus' | 'subway' | 'train' | 'tram' {
    const modeMap: Record<string, 'bus' | 'subway' | 'train' | 'tram'> = {
      'BUS': 'bus',
      'SUBWAY': 'subway',
      'TRAIN': 'train',
      'TRAM': 'tram'
    };
    return modeMap[vehicleType] || 'bus';
  }

  /**
   * 解码polyline字符串为坐标数组
   */
  private decodePolyline(encoded: string): LatLng[] {
    if (!encoded) return [];

    const points: LatLng[] = [];
    let index = 0;
    let lat = 0;
    let lng = 0;

    while (index < encoded.length) {
      let shift = 0;
      let result = 0;
      let byte: number;

      do {
        byte = encoded.charCodeAt(index++) - 63;
        result |= (byte & 0x1f) << shift;
        shift += 5;
      } while (byte >= 0x20);

      const deltaLat = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lat += deltaLat;

      shift = 0;
      result = 0;

      do {
        byte = encoded.charCodeAt(index++) - 63;
        result |= (byte & 0x1f) << shift;
        shift += 5;
      } while (byte >= 0x20);

      const deltaLng = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lng += deltaLng;

      points.push({
        lat: lat / 1e5,
        lng: lng / 1e5
      });
    }

    return points;
  }

  /**
   * 编码坐标数组为polyline字符串
   */
  private encodePolyline(points: LatLng[]): string {
    if (!points || points.length === 0) return '';

    let encoded = '';
    let prevLat = 0;
    let prevLng = 0;

    for (const point of points) {
      const lat = Math.round(point.lat * 1e5);
      const lng = Math.round(point.lng * 1e5);

      const deltaLat = lat - prevLat;
      const deltaLng = lng - prevLng;

      encoded += this.encodeSignedNumber(deltaLat);
      encoded += this.encodeSignedNumber(deltaLng);

      prevLat = lat;
      prevLng = lng;
    }

    return encoded;
  }

  /**
   * 编码有符号数字
   */
  private encodeSignedNumber(num: number): string {
    let sgn_num = num << 1;
    if (num < 0) {
      sgn_num = ~sgn_num;
    }
    return this.encodeNumber(sgn_num);
  }

  /**
   * 编码数字
   */
  private encodeNumber(num: number): string {
    let encoded = '';
    while (num >= 0x20) {
      encoded += String.fromCharCode((0x20 | (num & 0x1f)) + 63);
      num >>= 5;
    }
    encoded += String.fromCharCode(num + 63);
    return encoded;
  }
}
