// 地图配置管理组合式函数
import { ref, computed, watch, type Ref } from 'vue';
import { defaultMapConfig, mapStyles, mapTypes, logoPositions } from '../config/map-config';

// 地图配置接口
export interface MapConfigOptions {
  // 基础配置
  center?: { lat: number; lng: number };
  zoom?: number;
  language?: string;
  sourceType?: 'vector' | 'raster';
  mapType?: keyof typeof mapTypes;

  // 缩放限制
  minZoom?: number;
  maxZoom?: number;

  // 控件配置
  controls?: {
    copyright?: boolean;
    location?: boolean;
    navigation?: boolean;
    rotate?: boolean;
    scale?: boolean;
    zoom?: boolean;
    zoomSlider?: boolean;
  };

  // 样式配置
  style?: {
    logoPosition?: keyof typeof logoPositions;
    presetStyleId?: keyof typeof mapStyles;
    opacity?: number;
  };

  // API配置
  apiKey?: string;
}

// 默认控件配置
const defaultControls = {
  copyright: false,
  location: false,
  navigation: false,
  rotate: false,
  scale: false,
  zoom: true,
  zoomSlider: false
};

// 默认样式配置
const defaultStyle = {
  logoPosition: 'BOTTOM_LEFT' as keyof typeof logoPositions,
  presetStyleId: 'standard' as keyof typeof mapStyles,
  opacity: 1
};

/**
 * 地图配置管理组合式函数
 * @param initialConfig 初始配置
 * @returns 配置管理对象
 */
export function useMapConfig(initialConfig?: MapConfigOptions) {
  // 响应式配置状态
  const config = ref<MapConfigOptions>({
    center: initialConfig?.center || defaultMapConfig.defaultCenter,
    zoom: initialConfig?.zoom || defaultMapConfig.defaultZoom,
    language: initialConfig?.language || defaultMapConfig.defaultLanguage,
    sourceType: initialConfig?.sourceType || defaultMapConfig.defaultSourceType,
    mapType: initialConfig?.mapType || 'ROADMAP',
    minZoom: initialConfig?.minZoom || 2,
    maxZoom: initialConfig?.maxZoom || 20,
    controls: { ...defaultControls, ...initialConfig?.controls },
    style: { ...defaultStyle, ...initialConfig?.style },
    apiKey: initialConfig?.apiKey || defaultMapConfig.apiKey
  });

  // 计算属性：华为地图API所需的配置格式
  const hwMapOptions = computed((): any => ({
    center: config.value.center!,
    zoom: config.value.zoom!,
    language: config.value.language,
    sourceType: config.value.sourceType,
    mapType: config.value.mapType,
    minZoom: config.value.minZoom,
    maxZoom: config.value.maxZoom,
    copyrightControl: config.value.controls?.copyright,
    locationControl: config.value.controls?.location,
    navigationControl: config.value.controls?.navigation,
    rotateControl: config.value.controls?.rotate,
    scaleControl: config.value.controls?.scale,
    zoomControl: config.value.controls?.zoom,
    zoomSlider: config.value.controls?.zoomSlider,
    logoPosition: config.value.style?.logoPosition,
    presetStyleId: config.value.style?.presetStyleId
  }));

  // 更新配置的方法
  const updateConfig = (newConfig: Partial<MapConfigOptions>) => {
    config.value = {
      ...config.value,
      ...newConfig,
      controls: { ...config.value.controls, ...newConfig.controls },
      style: { ...config.value.style, ...newConfig.style }
    };
  };

  // 重置配置
  const resetConfig = () => {
    config.value = {
      center: defaultMapConfig.defaultCenter,
      zoom: defaultMapConfig.defaultZoom,
      language: defaultMapConfig.defaultLanguage,
      sourceType: defaultMapConfig.defaultSourceType,
      mapType: 'ROADMAP',
      minZoom: 2,
      maxZoom: 20,
      controls: { ...defaultControls },
      style: { ...defaultStyle },
      apiKey: defaultMapConfig.apiKey
    };
  };

  // 设置中心点
  const setCenter = (center: { lat: number; lng: number }) => {
    config.value.center = center;
  };

  // 设置缩放级别
  const setZoom = (zoom: number) => {
    config.value.zoom = Math.max(
      config.value.minZoom || 2,
      Math.min(config.value.maxZoom || 20, zoom)
    );
  };

  // 设置地图类型
  const setMapType = (mapType: keyof typeof mapTypes) => {
    config.value.mapType = mapType;
  };

  // 设置地图样式
  const setPresetStyle = (styleId: keyof typeof mapStyles) => {
    if (!config.value.style) {
      config.value.style = { ...defaultStyle };
    }
    config.value.style.presetStyleId = styleId;
  };

  // 切换控件显示
  const toggleControl = (controlName: keyof typeof defaultControls, enabled?: boolean) => {
    if (!config.value.controls) {
      config.value.controls = { ...defaultControls };
    }
    config.value.controls[controlName] = enabled !== undefined ? enabled : !config.value.controls[controlName];
  };

  // 设置透明度
  const setOpacity = (opacity: number) => {
    if (!config.value.style) {
      config.value.style = { ...defaultStyle };
    }
    config.value.style.opacity = Math.max(0, Math.min(1, opacity));
  };

  // 验证配置
  const validateConfig = () => {
    const errors: string[] = [];

    if (!config.value.center) {
      errors.push('地图中心点不能为空');
    } else {
      if (config.value.center.lat < -90 || config.value.center.lat > 90) {
        errors.push('纬度必须在-90到90之间');
      }
      if (config.value.center.lng < -180 || config.value.center.lng > 180) {
        errors.push('经度必须在-180到180之间');
      }
    }

    if (config.value.zoom !== undefined) {
      if (config.value.zoom < (config.value.minZoom || 2) || config.value.zoom > (config.value.maxZoom || 20)) {
        errors.push(`缩放级别必须在${config.value.minZoom || 2}到${config.value.maxZoom || 20}之间`);
      }
    }

    if (config.value.style?.opacity !== undefined) {
      if (config.value.style.opacity < 0 || config.value.style.opacity > 1) {
        errors.push('透明度必须在0到1之间');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  // 获取配置摘要
  const getConfigSummary = () => ({
    center: config.value.center,
    zoom: config.value.zoom,
    mapType: config.value.mapType,
    sourceType: config.value.sourceType,
    enabledControls: Object.entries(config.value.controls || {})
      .filter(([, enabled]) => enabled)
      .map(([name]) => name),
    style: config.value.style
  });

  return {
    // 响应式状态
    config: config as Ref<Required<MapConfigOptions>>,
    hwMapOptions,

    // 方法
    updateConfig,
    resetConfig,
    setCenter,
    setZoom,
    setMapType,
    setPresetStyle,
    toggleControl,
    setOpacity,
    validateConfig,
    getConfigSummary
  };
}
