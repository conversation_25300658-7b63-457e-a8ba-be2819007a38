// 信息窗管理组合式函数
import { ref, computed, type Ref } from 'vue';

// 信息窗数据接口
export interface InfoWindowData {
  id: string;
  position: { lat: number; lng: number };
  content: string | HTMLElement;
  title?: string;
  offset?: [number, number];
  maxWidth?: number;
  maxHeight?: number;
  closable?: boolean;
  visible?: boolean;
  zIndex?: number;
  className?: string;
  markerId?: string; // 关联的标记ID
}

// 信息窗事件处理器
export interface InfoWindowEventHandlers {
  onOpen?: (infoWindow: InfoWindowData) => void;
  onClose?: (infoWindow: InfoWindowData) => void;
  onClick?: (infoWindow: InfoWindowData, event: any) => void;
  onContentChange?: (infoWindow: InfoWindowData, content: string | HTMLElement) => void;
}

/**
 * 信息窗管理组合式函数
 * @param map 地图实例引用
 * @param eventHandlers 事件处理器
 * @returns 信息窗管理对象
 */
export function useInfoWindows(
  map: Ref<any>,
  eventHandlers?: InfoWindowEventHandlers
) {
  // 信息窗数据存储
  const infoWindows = ref<InfoWindowData[]>([]);
  const infoWindowInstances = ref<Map<string, any>>(new Map());
  
  // 当前打开的信息窗
  const activeInfoWindow = ref<InfoWindowData | null>(null);
  
  // 计算属性
  const visibleInfoWindows = computed(() => 
    infoWindows.value.filter(infoWindow => infoWindow.visible !== false)
  );
  
  const infoWindowCount = computed(() => infoWindows.value.length);
  const visibleInfoWindowCount = computed(() => visibleInfoWindows.value.length);

  // 生成唯一ID
  const generateId = () => {
    return 'infowindow_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  };

  // 创建华为地图信息窗实例
  const createInfoWindowInstance = (infoWindowData: InfoWindowData) => {
    if (!map.value) return null;

    const infoWindowOptions: any = {
      position: infoWindowData.position,
      content: infoWindowData.content,
      map: map.value
    };

    // 设置偏移量
    if (infoWindowData.offset) {
      infoWindowOptions.offset = infoWindowData.offset;
    }

    const infoWindowInstance = new window.HWMapJsSDK.HWInfoWindow(infoWindowOptions);

    // 绑定事件
    bindInfoWindowEvents(infoWindowInstance, infoWindowData);

    return infoWindowInstance;
  };

  // 绑定信息窗事件
  const bindInfoWindowEvents = (infoWindowInstance: any, infoWindowData: InfoWindowData) => {
    if (!eventHandlers) return;

    // 关闭事件
    if (eventHandlers.onClose) {
      infoWindowInstance.addListener('close', () => {
        infoWindowData.visible = false;
        if (activeInfoWindow.value?.id === infoWindowData.id) {
          activeInfoWindow.value = null;
        }
        eventHandlers.onClose!(infoWindowData);
      });
    }
  };

  // 添加信息窗
  const addInfoWindow = (infoWindowData: Omit<InfoWindowData, 'id'> & { id?: string }): string => {
    const id = infoWindowData.id || generateId();
    const newInfoWindow: InfoWindowData = {
      ...infoWindowData,
      id,
      visible: infoWindowData.visible !== false,
      closable: infoWindowData.closable !== false
    };

    // 创建信息窗实例
    const infoWindowInstance = createInfoWindowInstance(newInfoWindow);
    if (infoWindowInstance) {
      infoWindowInstances.value.set(id, infoWindowInstance);
      infoWindows.value.push(newInfoWindow);
    }

    return id;
  };

  // 打开信息窗
  const openInfoWindow = (id: string, marker?: any): boolean => {
    const infoWindow = getInfoWindow(id);
    const infoWindowInstance = infoWindowInstances.value.get(id);
    
    if (!infoWindow || !infoWindowInstance) return false;

    // 关闭其他信息窗（通常一次只显示一个）
    closeAllInfoWindows();

    // 打开信息窗
    if (marker) {
      infoWindowInstance.open(marker);
    } else {
      infoWindowInstance.open();
    }

    infoWindow.visible = true;
    activeInfoWindow.value = infoWindow;

    eventHandlers?.onOpen?.(infoWindow);
    return true;
  };

  // 关闭信息窗
  const closeInfoWindow = (id: string): boolean => {
    const infoWindow = getInfoWindow(id);
    const infoWindowInstance = infoWindowInstances.value.get(id);
    
    if (!infoWindow || !infoWindowInstance) return false;

    infoWindowInstance.close();
    infoWindow.visible = false;
    
    if (activeInfoWindow.value?.id === id) {
      activeInfoWindow.value = null;
    }

    eventHandlers?.onClose?.(infoWindow);
    return true;
  };

  // 关闭所有信息窗
  const closeAllInfoWindows = (): number => {
    let closedCount = 0;
    infoWindows.value.forEach(infoWindow => {
      if (infoWindow.visible) {
        closeInfoWindow(infoWindow.id);
        closedCount++;
      }
    });
    return closedCount;
  };

  // 删除信息窗
  const removeInfoWindow = (id: string): boolean => {
    const infoWindowInstance = infoWindowInstances.value.get(id);
    if (infoWindowInstance) {
      infoWindowInstance.close();
      infoWindowInstances.value.delete(id);
    }

    const index = infoWindows.value.findIndex(infoWindow => infoWindow.id === id);
    if (index > -1) {
      infoWindows.value.splice(index, 1);
      
      if (activeInfoWindow.value?.id === id) {
        activeInfoWindow.value = null;
      }
      
      return true;
    }
    return false;
  };

  // 清空所有信息窗
  const clearInfoWindows = (): number => {
    const count = infoWindows.value.length;
    infoWindowInstances.value.forEach(infoWindowInstance => {
      infoWindowInstance.close();
    });
    infoWindowInstances.value.clear();
    infoWindows.value = [];
    activeInfoWindow.value = null;
    return count;
  };

  // 更新信息窗
  const updateInfoWindow = (id: string, updates: Partial<InfoWindowData>): boolean => {
    const infoWindowIndex = infoWindows.value.findIndex(infoWindow => infoWindow.id === id);
    if (infoWindowIndex === -1) return false;

    const infoWindow = infoWindows.value[infoWindowIndex];
    const infoWindowInstance = infoWindowInstances.value.get(id);
    
    if (!infoWindowInstance) return false;

    // 更新数据
    Object.assign(infoWindow, updates);

    // 更新实例
    if (updates.position) {
      infoWindowInstance.setPosition(updates.position);
    }
    if (updates.content !== undefined) {
      infoWindowInstance.setContent(updates.content);
      eventHandlers?.onContentChange?.(infoWindow, updates.content);
    }

    return true;
  };

  // 获取信息窗
  const getInfoWindow = (id: string): InfoWindowData | undefined => {
    return infoWindows.value.find(infoWindow => infoWindow.id === id);
  };

  // 获取信息窗实例
  const getInfoWindowInstance = (id: string): any => {
    return infoWindowInstances.value.get(id);
  };

  // 根据标记ID查找信息窗
  const findInfoWindowsByMarkerId = (markerId: string): InfoWindowData[] => {
    return infoWindows.value.filter(infoWindow => infoWindow.markerId === markerId);
  };

  // 为标记创建信息窗
  const createInfoWindowForMarker = (
    markerId: string,
    markerPosition: { lat: number; lng: number },
    content: string | HTMLElement,
    options?: Partial<InfoWindowData>
  ): string => {
    const infoWindowData: Omit<InfoWindowData, 'id'> = {
      position: markerPosition,
      content,
      markerId,
      ...options
    };

    return addInfoWindow(infoWindowData);
  };

  // 显示标记的信息窗
  const showMarkerInfoWindow = (markerId: string, marker?: any): boolean => {
    const infoWindows = findInfoWindowsByMarkerId(markerId);
    if (infoWindows.length === 0) return false;

    // 显示第一个关联的信息窗
    return openInfoWindow(infoWindows[0].id, marker);
  };

  // 隐藏标记的信息窗
  const hideMarkerInfoWindow = (markerId: string): boolean => {
    const infoWindows = findInfoWindowsByMarkerId(markerId);
    let hiddenCount = 0;
    
    infoWindows.forEach(infoWindow => {
      if (closeInfoWindow(infoWindow.id)) {
        hiddenCount++;
      }
    });

    return hiddenCount > 0;
  };

  // 切换信息窗显示状态
  const toggleInfoWindow = (id: string, marker?: any): boolean => {
    const infoWindow = getInfoWindow(id);
    if (!infoWindow) return false;

    if (infoWindow.visible) {
      return closeInfoWindow(id);
    } else {
      return openInfoWindow(id, marker);
    }
  };

  // 设置信息窗内容
  const setInfoWindowContent = (id: string, content: string | HTMLElement): boolean => {
    return updateInfoWindow(id, { content });
  };

  // 移动信息窗位置
  const moveInfoWindow = (id: string, position: { lat: number; lng: number }): boolean => {
    return updateInfoWindow(id, { position });
  };

  return {
    // 响应式状态
    infoWindows,
    visibleInfoWindows,
    activeInfoWindow,
    infoWindowCount,
    visibleInfoWindowCount,

    // 方法
    addInfoWindow,
    openInfoWindow,
    closeInfoWindow,
    closeAllInfoWindows,
    removeInfoWindow,
    clearInfoWindows,
    updateInfoWindow,
    getInfoWindow,
    getInfoWindowInstance,
    findInfoWindowsByMarkerId,
    createInfoWindowForMarker,
    showMarkerInfoWindow,
    hideMarkerInfoWindow,
    toggleInfoWindow,
    setInfoWindowContent,
    moveInfoWindow
  };
}
