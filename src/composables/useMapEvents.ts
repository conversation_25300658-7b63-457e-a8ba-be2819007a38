// 地图事件管理组合式函数
import { ref, onUnmounted, type Ref } from 'vue';

// 地图事件类型定义
export interface MapEventData {
  coordinate: [number, number];
  pixel: [number, number];
  latLng?: { lat: number; lng: number };
}

export interface MapEventHandlers {
  onClick?: (event: MapEventData) => void;
  onDoubleClick?: (event: MapEventData) => void;
  onRightClick?: (event: MapEventData) => void;
  onMouseMove?: (event: MapEventData) => void;
  onMouseDown?: (event: MapEventData) => void;
  onMouseUp?: (event: MapEventData) => void;
  onDrag?: (event: MapEventData) => void;
  onMoveStart?: () => void;
  onMoveEnd?: () => void;
  onCenterChanged?: (center: { lat: number; lng: number }) => void;
  onZoomChanged?: (zoom: number) => void;
  onHeadingChanged?: (heading: number) => void;
}

// 事件历史记录
export interface EventRecord {
  id: string;
  type: string;
  timestamp: number;
  data?: any;
}

/**
 * 地图事件管理组合式函数
 * @param map 地图实例引用
 * @param handlers 事件处理器
 * @returns 事件管理对象
 */
export function useMapEvents(
  map: Ref<any>,
  handlers?: MapEventHandlers
) {
  // 事件历史记录
  const eventHistory = ref<EventRecord[]>([]);
  const maxHistorySize = 100;

  // 当前地图状态
  const currentCenter = ref<{ lat: number; lng: number }>();
  const currentZoom = ref<number>();
  const currentHeading = ref<number>();

  // 事件监听器存储
  const eventListeners = new Map<string, Function[]>();

  // 生成唯一ID的计数器
  let eventIdCounter = 0;

  // 添加事件到历史记录
  const addToHistory = (type: string, data?: any) => {
    const record: EventRecord = {
      id: `${Date.now()}-${type}-${++eventIdCounter}`,
      type,
      timestamp: Date.now(),
      data
    };

    eventHistory.value.unshift(record);

    // 限制历史记录大小
    if (eventHistory.value.length > maxHistorySize) {
      eventHistory.value = eventHistory.value.slice(0, maxHistorySize);
    }
  };

  // 转换坐标格式
  const convertCoordinate = (coordinate: [number, number]): { lat: number; lng: number } => {
    // 华为地图API返回的coordinate是墨卡托投影坐标，需要转换
    // 这里假设已经是经纬度坐标，实际使用时可能需要调用API转换
    return { lat: coordinate[1], lng: coordinate[0] };
  };

  // 绑定地图事件
  const bindMapEvents = () => {
    if (!map.value) return;

    // 点击事件
    const clickHandler = (event: any) => {
      const eventData: MapEventData = {
        coordinate: event.coordinate,
        pixel: event.pixel,
        latLng: convertCoordinate(event.coordinate)
      };

      addToHistory('click', eventData);
      handlers?.onClick?.(eventData);
    };
    map.value.on('click', clickHandler);
    addListener('click', clickHandler);

    // 双击事件
    const dblClickHandler = (event: any) => {
      const eventData: MapEventData = {
        coordinate: event.coordinate,
        pixel: event.pixel,
        latLng: convertCoordinate(event.coordinate)
      };

      addToHistory('dblclick', eventData);
      handlers?.onDoubleClick?.(eventData);
    };
    map.value.on('dblclick', dblClickHandler);
    addListener('dblclick', dblClickHandler);

    // 右键点击事件
    const contextMenuHandler = (event: any) => {
      const eventData: MapEventData = {
        coordinate: event.coordinate,
        pixel: event.pixel,
        latLng: convertCoordinate(event.coordinate)
      };

      addToHistory('contextmenu', eventData);
      handlers?.onRightClick?.(eventData);
    };
    map.value.on('contextmenu', contextMenuHandler);
    addListener('contextmenu', contextMenuHandler);

    // 鼠标移动事件
    const mouseMoveHandler = (event: any) => {
      const eventData: MapEventData = {
        coordinate: event.coordinate,
        pixel: event.pixel,
        latLng: convertCoordinate(event.coordinate)
      };

      handlers?.onMouseMove?.(eventData);
    };
    map.value.on('pointermove', mouseMoveHandler);
    addListener('pointermove', mouseMoveHandler);

    // 鼠标按下事件
    const mouseDownHandler = (event: any) => {
      const eventData: MapEventData = {
        coordinate: event.coordinate,
        pixel: event.pixel,
        latLng: convertCoordinate(event.coordinate)
      };

      addToHistory('mousedown', eventData);
      handlers?.onMouseDown?.(eventData);
    };
    map.value.on('pointerdown', mouseDownHandler);
    addListener('pointerdown', mouseDownHandler);

    // 鼠标松开事件
    const mouseUpHandler = (event: any) => {
      const eventData: MapEventData = {
        coordinate: event.coordinate,
        pixel: event.pixel,
        latLng: convertCoordinate(event.coordinate)
      };

      addToHistory('mouseup', eventData);
      handlers?.onMouseUp?.(eventData);
    };
    map.value.on('pointerup', mouseUpHandler);
    addListener('pointerup', mouseUpHandler);

    // 拖拽事件
    const dragHandler = (event: any) => {
      const eventData: MapEventData = {
        coordinate: event.coordinate,
        pixel: event.pixel,
        latLng: convertCoordinate(event.coordinate)
      };

      handlers?.onDrag?.(eventData);
    };
    map.value.on('pointerdrag', dragHandler);
    addListener('pointerdrag', dragHandler);

    // 移动开始事件
    const moveStartHandler = () => {
      addToHistory('movestart');
      handlers?.onMoveStart?.();
    };
    map.value.on('movestart', moveStartHandler);
    addListener('movestart', moveStartHandler);

    // 移动结束事件
    const moveEndHandler = () => {
      addToHistory('moveend');
      handlers?.onMoveEnd?.();
    };
    map.value.on('moveend', moveEndHandler);
    addListener('moveend', moveEndHandler);

    // 中心点变化事件
    const centerChangedHandler = () => {
      const center = map.value.getCenter();
      currentCenter.value = center;
      addToHistory('centerChanged', center);
      handlers?.onCenterChanged?.(center);
    };
    map.value.onCenterChanged(centerChangedHandler);
    addListener('centerChanged', centerChangedHandler);

    // 缩放级别变化事件
    const zoomChangedHandler = () => {
      const zoom = map.value.getZoom();
      currentZoom.value = zoom;
      addToHistory('zoomChanged', zoom);
      handlers?.onZoomChanged?.(zoom);
    };
    map.value.onZoomChanged(zoomChangedHandler);
    addListener('zoomChanged', zoomChangedHandler);

    // 方向变化事件
    const headingChangedHandler = () => {
      const heading = map.value.getHeading();
      currentHeading.value = heading;
      addToHistory('headingChanged', heading);
      handlers?.onHeadingChanged?.(heading);
    };
    map.value.onHeadingChanged(headingChangedHandler);
    addListener('headingChanged', headingChangedHandler);
  };

  // 添加事件监听器到存储
  const addListener = (eventType: string, handler: Function) => {
    if (!eventListeners.has(eventType)) {
      eventListeners.set(eventType, []);
    }
    eventListeners.get(eventType)!.push(handler);
  };

  // 解绑所有事件
  const unbindAllEvents = () => {
    if (!map.value) return;

    eventListeners.forEach((handlers, eventType) => {
      handlers.forEach(handler => {
        if (eventType === 'centerChanged' || eventType === 'zoomChanged' || eventType === 'headingChanged') {
          // 这些事件使用不同的解绑方法
          return;
        }
        map.value.un(eventType, handler);
      });
    });

    eventListeners.clear();
  };

  // 清空事件历史
  const clearHistory = () => {
    eventHistory.value = [];
  };

  // 获取最近的事件
  const getRecentEvents = (count: number = 10) => {
    return eventHistory.value.slice(0, count);
  };

  // 获取特定类型的事件
  const getEventsByType = (type: string) => {
    return eventHistory.value.filter(event => event.type === type);
  };

  // 组件卸载时清理
  onUnmounted(() => {
    unbindAllEvents();
  });

  return {
    // 响应式状态
    eventHistory,
    currentCenter,
    currentZoom,
    currentHeading,

    // 方法
    bindMapEvents,
    unbindAllEvents,
    clearHistory,
    getRecentEvents,
    getEventsByType,
    addToHistory
  };
}
