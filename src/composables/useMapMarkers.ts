// 地图标记管理组合式函数
import { ref, computed, watch, type Ref } from 'vue';

// 标记数据接口
export interface MarkerData {
  id: string;
  position: { lat: number; lng: number };
  title?: string;
  content?: string;
  icon?: string | MarkerIconConfig;
  label?: string | MarkerLabelConfig;
  draggable?: boolean;
  animation?: 'DROP' | 'BOUNCE' | null;
  zIndex?: number;
  visible?: boolean;
  properties?: Record<string, any>;
}

// 标记图标配置
export interface MarkerIconConfig {
  url?: string;
  anchor?: [number, number];
  anchorUnit?: 'fraction' | 'pixels';
  scale?: number;
  rotation?: number;
  opacity?: number;
}

// 标记标签配置
export interface MarkerLabelConfig {
  text: string;
  color?: string;
  fontSize?: string;
  fontFamily?: string;
  offsetX?: number;
  offsetY?: number;
  strokeColor?: string;
  strokeWeight?: number;
}

// 标记事件处理器
export interface MarkerEventHandlers {
  onClick?: (marker: MarkerData, event: any) => void;
  onDoubleClick?: (marker: MarkerData, event: any) => void;
  onRightClick?: (marker: MarkerData, event: any) => void;
  onDragStart?: (marker: MarkerData, event: any) => void;
  onDrag?: (marker: MarkerData, event: any) => void;
  onDragEnd?: (marker: MarkerData, event: any) => void;
  onMouseOver?: (marker: MarkerData, event: any) => void;
  onMouseOut?: (marker: MarkerData, event: any) => void;
}

/**
 * 地图标记管理组合式函数
 * @param map 地图实例引用
 * @param eventHandlers 事件处理器
 * @returns 标记管理对象
 */
export function useMapMarkers(
  map: Ref<any>,
  eventHandlers?: MarkerEventHandlers
) {
  // 标记数据存储
  const markers = ref<MarkerData[]>([]);
  const markerInstances = ref<Map<string, any>>(new Map());
  
  // 选中的标记
  const selectedMarker = ref<MarkerData | null>(null);
  
  // 计算属性
  const visibleMarkers = computed(() => 
    markers.value.filter(marker => marker.visible !== false)
  );
  
  const markerCount = computed(() => markers.value.length);
  const visibleMarkerCount = computed(() => visibleMarkers.value.length);

  // 生成唯一ID
  const generateId = () => {
    return 'marker_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  };

  // 创建华为地图标记实例
  const createMarkerInstance = (markerData: MarkerData) => {
    if (!map.value) return null;

    const markerOptions: any = {
      position: markerData.position,
      map: map.value,
      draggable: markerData.draggable || false,
      animation: markerData.animation || null,
      zIndex: markerData.zIndex || 0
    };

    // 设置图标
    if (markerData.icon) {
      markerOptions.icon = markerData.icon;
    }

    // 设置标签
    if (markerData.label) {
      markerOptions.label = markerData.label;
    }

    // 设置自定义属性
    if (markerData.properties) {
      markerOptions.properties = markerData.properties;
    }

    const markerInstance = new window.HWMapJsSDK.HWMarker(markerOptions);

    // 绑定事件
    bindMarkerEvents(markerInstance, markerData);

    return markerInstance;
  };

  // 绑定标记事件
  const bindMarkerEvents = (markerInstance: any, markerData: MarkerData) => {
    if (!eventHandlers) return;

    // 点击事件
    if (eventHandlers.onClick) {
      markerInstance.addListener('click', (event: any) => {
        eventHandlers.onClick!(markerData, event);
      });
    }

    // 双击事件
    if (eventHandlers.onDoubleClick) {
      markerInstance.addListener('dbclick', (event: any) => {
        eventHandlers.onDoubleClick!(markerData, event);
      });
    }

    // 右键点击事件
    if (eventHandlers.onRightClick) {
      markerInstance.addListener('contextmenu', (event: any) => {
        eventHandlers.onRightClick!(markerData, event);
      });
    }

    // 鼠标悬停事件
    if (eventHandlers.onMouseOver) {
      markerInstance.addListener('mouseOver', (event: any) => {
        eventHandlers.onMouseOver!(markerData, event);
      });
    }

    // 鼠标离开事件
    if (eventHandlers.onMouseOut) {
      markerInstance.addListener('mouseOut', (event: any) => {
        eventHandlers.onMouseOut!(markerData, event);
      });
    }

    // 拖拽相关事件
    if (markerData.draggable) {
      if (eventHandlers.onDragStart) {
        markerInstance.addListener('dragstart', (event: any) => {
          eventHandlers.onDragStart!(markerData, event);
        });
      }

      if (eventHandlers.onDrag) {
        markerInstance.addListener('drag', (event: any) => {
          eventHandlers.onDrag!(markerData, event);
        });
      }

      if (eventHandlers.onDragEnd) {
        markerInstance.addListener('dragend', (event: any) => {
          // 更新标记位置
          const newPosition = markerInstance.getPosition();
          markerData.position = newPosition;
          eventHandlers.onDragEnd!(markerData, event);
        });
      }
    }
  };

  // 添加标记
  const addMarker = (markerData: Omit<MarkerData, 'id'> & { id?: string }): string => {
    const id = markerData.id || generateId();
    const newMarker: MarkerData = {
      ...markerData,
      id,
      visible: markerData.visible !== false
    };

    // 创建标记实例
    const markerInstance = createMarkerInstance(newMarker);
    if (markerInstance) {
      markerInstances.value.set(id, markerInstance);
      markers.value.push(newMarker);
    }

    return id;
  };

  // 批量添加标记
  const addMarkers = (markersData: (Omit<MarkerData, 'id'> & { id?: string })[]): string[] => {
    return markersData.map(markerData => addMarker(markerData));
  };

  // 删除标记
  const removeMarker = (id: string): boolean => {
    const markerInstance = markerInstances.value.get(id);
    if (markerInstance) {
      markerInstance.setMap(null);
      markerInstances.value.delete(id);
    }

    const index = markers.value.findIndex(marker => marker.id === id);
    if (index > -1) {
      markers.value.splice(index, 1);
      
      // 如果删除的是选中的标记，清空选中状态
      if (selectedMarker.value?.id === id) {
        selectedMarker.value = null;
      }
      
      return true;
    }
    return false;
  };

  // 批量删除标记
  const removeMarkers = (ids: string[]): number => {
    let removedCount = 0;
    ids.forEach(id => {
      if (removeMarker(id)) {
        removedCount++;
      }
    });
    return removedCount;
  };

  // 清空所有标记
  const clearMarkers = (): number => {
    const count = markers.value.length;
    markerInstances.value.forEach(markerInstance => {
      markerInstance.setMap(null);
    });
    markerInstances.value.clear();
    markers.value = [];
    selectedMarker.value = null;
    return count;
  };

  // 更新标记
  const updateMarker = (id: string, updates: Partial<MarkerData>): boolean => {
    const markerIndex = markers.value.findIndex(marker => marker.id === id);
    if (markerIndex === -1) return false;

    const marker = markers.value[markerIndex];
    const markerInstance = markerInstances.value.get(id);
    
    if (!markerInstance) return false;

    // 更新数据
    Object.assign(marker, updates);

    // 更新实例
    if (updates.position) {
      markerInstance.setPosition(updates.position);
    }
    if (updates.icon !== undefined) {
      markerInstance.setIcon(updates.icon);
    }
    if (updates.label !== undefined) {
      markerInstance.setLabel(updates.label);
    }
    if (updates.draggable !== undefined) {
      markerInstance.setDraggable(updates.draggable);
    }
    if (updates.animation !== undefined) {
      markerInstance.setAnimation(updates.animation);
    }
    if (updates.zIndex !== undefined) {
      markerInstance.setZIndex(updates.zIndex);
    }
    if (updates.visible !== undefined) {
      markerInstance.setMap(updates.visible ? map.value : null);
    }

    return true;
  };

  // 获取标记
  const getMarker = (id: string): MarkerData | undefined => {
    return markers.value.find(marker => marker.id === id);
  };

  // 获取标记实例
  const getMarkerInstance = (id: string): any => {
    return markerInstances.value.get(id);
  };

  // 显示/隐藏标记
  const toggleMarkerVisibility = (id: string): boolean => {
    const marker = getMarker(id);
    if (!marker) return false;

    const newVisibility = !marker.visible;
    return updateMarker(id, { visible: newVisibility });
  };

  // 选中标记
  const selectMarker = (id: string): boolean => {
    const marker = getMarker(id);
    if (!marker) return false;

    selectedMarker.value = marker;
    return true;
  };

  // 取消选中
  const deselectMarker = () => {
    selectedMarker.value = null;
  };

  // 根据位置查找标记
  const findMarkersByPosition = (
    position: { lat: number; lng: number }, 
    tolerance: number = 0.0001
  ): MarkerData[] => {
    return markers.value.filter(marker => {
      const latDiff = Math.abs(marker.position.lat - position.lat);
      const lngDiff = Math.abs(marker.position.lng - position.lng);
      return latDiff <= tolerance && lngDiff <= tolerance;
    });
  };

  // 获取标记边界
  const getMarkersBounds = (): { ne: { lat: number; lng: number }; sw: { lat: number; lng: number } } | null => {
    if (visibleMarkers.value.length === 0) return null;

    let minLat = Infinity, maxLat = -Infinity;
    let minLng = Infinity, maxLng = -Infinity;

    visibleMarkers.value.forEach(marker => {
      minLat = Math.min(minLat, marker.position.lat);
      maxLat = Math.max(maxLat, marker.position.lat);
      minLng = Math.min(minLng, marker.position.lng);
      maxLng = Math.max(maxLng, marker.position.lng);
    });

    return {
      ne: { lat: maxLat, lng: maxLng },
      sw: { lat: minLat, lng: minLng }
    };
  };

  // 适应所有标记到视图
  const fitMarkersToView = (): boolean => {
    if (!map.value) return false;

    const bounds = getMarkersBounds();
    if (!bounds) return false;

    map.value.fitBounds(bounds);
    return true;
  };

  return {
    // 响应式状态
    markers,
    visibleMarkers,
    selectedMarker,
    markerCount,
    visibleMarkerCount,

    // 方法
    addMarker,
    addMarkers,
    removeMarker,
    removeMarkers,
    clearMarkers,
    updateMarker,
    getMarker,
    getMarkerInstance,
    toggleMarkerVisibility,
    selectMarker,
    deselectMarker,
    findMarkersByPosition,
    getMarkersBounds,
    fitMarkersToView
  };
}
