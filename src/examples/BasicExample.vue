<template>
  <div class="basic-example">
    <h2>基础地图示例</h2>
    <p>这是一个简单的华为地图组件使用示例</p>

    <div class="example-container">
      <HuaweiMap
        ref="mapRef"
        :center="{ lat: 39.9042, lng: 116.4074 }"
        :zoom="10"
        width="100%"
        height="400px"
        :zoom-control="true"
        :scale-control="true"
        :location-control="true"
        @map-ready="onMapReady"
        @map-click="onMapClick"
      />
    </div>

    <div class="controls">
      <h3>地图控制</h3>
      <div class="control-buttons">
        <button @click="addRandomMarker" class="btn primary">添加随机标记</button>
        <button @click="addInfoWindow" class="btn secondary">添加信息窗</button>
        <button @click="clearAll" class="btn danger">清空所有</button>
        <button @click="fitToView" class="btn info">适应视图</button>
      </div>
    </div>

    <div class="info-panel">
      <h3>操作日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import HuaweiMap from '../components/HuaweiMap.vue';

// 地图引用
const mapRef = ref<InstanceType<typeof HuaweiMap>>();

// 日志记录
const logs = ref<Array<{ time: string; message: string }>>([]);

// 添加日志
const addLog = (message: string) => {
  const time = new Date().toLocaleTimeString();
  logs.value.unshift({ time, message });
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20);
  }
};

// 地图就绪事件
const onMapReady = (map: any) => {
  addLog('地图加载完成');
  console.log('地图实例:', map);
};

// 地图点击事件
const onMapClick = (event: any) => {
  // 华为地图API返回的coordinate可能需要转换
  // 这里假设coordinate已经是[lng, lat]格式
  const lng = event.coordinate[0];
  const lat = event.coordinate[1];
  addLog(`地图点击: ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
};

// 添加随机标记
const addRandomMarker = () => {
  if (!mapRef.value) return;

  const lat = 39.9042 + (Math.random() - 0.5) * 0.1;
  const lng = 116.4074 + (Math.random() - 0.5) * 0.1;

  const markerId = mapRef.value.addMarker({
    position: { lat, lng },
    title: `随机标记 ${Date.now()}`,
    content: `这是一个随机生成的标记，位置: ${lat.toFixed(6)}, ${lng.toFixed(6)}`,
    draggable: true,
    animation: Math.random() > 0.5 ? 'DROP' : 'BOUNCE'
  });

  addLog(`添加标记: ${markerId}`);
};

// 添加信息窗
const addInfoWindow = () => {
  if (!mapRef.value) return;

  const lat = 39.9042 + (Math.random() - 0.5) * 0.05;
  const lng = 116.4074 + (Math.random() - 0.5) * 0.05;

  const infoWindowId = mapRef.value.addInfoWindow({
    position: { lat, lng },
    content: `
      <div style="padding: 10px; max-width: 200px;">
        <h4 style="margin: 0 0 8px 0; color: #333;">信息窗示例</h4>
        <p style="margin: 0; font-size: 14px; color: #666;">
          这是一个信息窗示例，位置: ${lat.toFixed(4)}, ${lng.toFixed(4)}
        </p>
        <p style="margin: 8px 0 0 0; font-size: 12px; color: #999;">
          点击时间: ${new Date().toLocaleString()}
        </p>
      </div>
    `,
    closable: true
  });

  // 立即打开信息窗
  mapRef.value.openInfoWindow(infoWindowId);
  addLog(`添加信息窗: ${infoWindowId}`);
};

// 清空所有
const clearAll = () => {
  if (!mapRef.value) return;

  const markerCount = mapRef.value.clearMarkers();
  const infoWindowCount = mapRef.value.clearInfoWindows();

  addLog(`清空 ${markerCount} 个标记和 ${infoWindowCount} 个信息窗`);
};

// 适应视图
const fitToView = () => {
  if (!mapRef.value) return;

  const success = mapRef.value.fitMarkersToView();
  if (success) {
    addLog('已适应所有标记到视图');
  } else {
    addLog('没有可见的标记');
  }
};
</script>

<style scoped>
.basic-example {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.basic-example h2 {
  color: #2c3e50;
  margin-bottom: 8px;
}

.basic-example p {
  color: #7f8c8d;
  margin-bottom: 20px;
}

.example-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.controls {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.controls h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
}

.control-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.btn.primary {
  background: #007bff;
  color: white;
}

.btn.primary:hover {
  background: #0056b3;
}

.btn.secondary {
  background: #6c757d;
  color: white;
}

.btn.secondary:hover {
  background: #545b62;
}

.btn.danger {
  background: #dc3545;
  color: white;
}

.btn.danger:hover {
  background: #c82333;
}

.btn.info {
  background: #17a2b8;
  color: white;
}

.btn.info:hover {
  background: #138496;
}

.info-panel {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.info-panel h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 12px;
  background: #f8f9fa;
}

.log-item {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 13px;
}

.log-item:last-child {
  margin-bottom: 0;
}

.log-time {
  color: #6c757d;
  font-family: monospace;
  min-width: 80px;
}

.log-message {
  color: #495057;
}
</style>
