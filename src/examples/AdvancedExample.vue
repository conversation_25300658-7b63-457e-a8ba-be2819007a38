<template>
  <div class="advanced-example">
    <h2>高级地图示例</h2>
    <p>展示华为地图组件的高级功能，包括事件监控、标记管理和信息窗</p>

    <div class="example-layout">
      <div class="map-container">
        <HuaweiMap
          ref="mapRef"
          :center="mapCenter"
          :zoom="mapZoom"
          :language="mapLanguage"
          :source-type="sourceType"
          :map-type="mapType"
          :zoom-control="true"
          :scale-control="true"
          :location-control="true"
          :enable-event-monitor="true"
          width="100%"
          height="500px"
          @map-ready="onMapReady"
          @map-click="onMapClick"
          @marker-click="onMarkerClick"
          @center-changed="onCenterChanged"
          @zoom-changed="onZoomChanged"
        />
      </div>

      <div class="control-panel">
        <div class="section">
          <h3>地图设置</h3>
          <div class="form-group">
            <label>地图类型:</label>
            <select v-model="mapType">
              <option value="ROADMAP">基础地图</option>
              <option value="TERRAIN">地形图</option>
            </select>
          </div>
          <div class="form-group">
            <label>瓦片类型:</label>
            <select v-model="sourceType">
              <option value="vector">矢量</option>
              <option value="raster">栅格</option>
            </select>
          </div>
          <div class="form-group">
            <label>语言:</label>
            <select v-model="mapLanguage">
              <option value="zh">中文</option>
              <option value="en">English</option>
            </select>
          </div>
        </div>

        <div class="section">
          <h3>预设位置</h3>
          <div class="preset-buttons">
            <button
              v-for="city in presetCities"
              :key="city.name"
              @click="goToCity(city)"
              class="preset-btn"
            >
              {{ city.name }}
            </button>
          </div>
        </div>

        <div class="section">
          <h3>批量操作</h3>
          <div class="batch-buttons">
            <button @click="addMultipleMarkers" class="btn primary">添加多个标记</button>
            <button @click="createMarkerCluster" class="btn secondary">创建标记集群</button>
            <button @click="showAllInfoWindows" class="btn info">显示所有信息窗</button>
            <button @click="clearEverything" class="btn danger">清空所有</button>
          </div>
        </div>

        <div class="section">
          <h3>统计信息</h3>
          <div class="stats">
            <div class="stat-item">
              <span class="stat-label">标记数量:</span>
              <span class="stat-value">{{ markerCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">信息窗数量:</span>
              <span class="stat-value">{{ infoWindowCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">当前中心:</span>
              <span class="stat-value">{{ formatCoordinate(mapCenter) }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">当前缩放:</span>
              <span class="stat-value">{{ mapZoom }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="event-log">
      <h3>事件日志</h3>
      <div class="log-controls">
        <button @click="clearLogs" class="btn small">清空日志</button>
        <label class="checkbox-label">
          <input v-model="autoScroll" type="checkbox" />
          <span>自动滚动</span>
        </label>
      </div>
      <div ref="logContainer" class="log-container" :class="{ 'auto-scroll': autoScroll }">
        <div v-for="(log, index) in eventLogs" :key="index" class="log-entry" :class="`log-${log.type}`">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-type">{{ log.type }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import HuaweiMap from '../components/HuaweiMap.vue';

// 地图引用
const mapRef = ref<InstanceType<typeof HuaweiMap>>();
const logContainer = ref<HTMLElement>();

// 地图配置
const mapCenter = ref({ lat: 39.9042, lng: 116.4074 });
const mapZoom = ref(10);
const mapLanguage = ref('zh');
const sourceType = ref<'vector' | 'raster'>('raster');
const mapType = ref<'ROADMAP' | 'TERRAIN'>('ROADMAP');

// 日志配置
const autoScroll = ref(true);
const eventLogs = ref<Array<{ time: string; type: string; message: string }>>([]);

// 预设城市
const presetCities = [
  { name: '北京', lat: 39.9042, lng: 116.4074 },
  { name: '上海', lat: 31.2304, lng: 121.4737 },
  { name: '广州', lat: 23.1291, lng: 113.2644 },
  { name: '深圳', lat: 22.5431, lng: 114.0579 },
  { name: '杭州', lat: 30.2741, lng: 120.1551 },
  { name: '南京', lat: 32.0603, lng: 118.7969 }
];

// 计算属性
const markerCount = computed(() => mapRef.value?.markerCount || 0);
const infoWindowCount = computed(() => mapRef.value?.infoWindowCount || 0);

// 添加日志
const addLog = (type: string, message: string) => {
  const time = new Date().toLocaleTimeString();
  eventLogs.value.unshift({ time, type, message });
  if (eventLogs.value.length > 100) {
    eventLogs.value = eventLogs.value.slice(0, 100);
  }

  if (autoScroll.value) {
    nextTick(() => {
      if (logContainer.value) {
        logContainer.value.scrollTop = 0;
      }
    });
  }
};

// 格式化坐标
const formatCoordinate = (coord: { lat: number; lng: number }) => {
  return `${coord.lat.toFixed(4)}, ${coord.lng.toFixed(4)}`;
};

// 事件处理
const onMapReady = (map: any) => {
  addLog('system', '地图加载完成');
};

const onMapClick = (event: any) => {
  const lng = event.coordinate[0];
  const lat = event.coordinate[1];
  addLog('click', `地图点击: ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
};

const onMarkerClick = (marker: any, event: any) => {
  addLog('marker', `标记点击: ${marker.title || marker.id}`);
};

const onCenterChanged = (center: { lat: number; lng: number }) => {
  mapCenter.value = center;
  addLog('change', `中心点变化: ${formatCoordinate(center)}`);
};

const onZoomChanged = (zoom: number) => {
  mapZoom.value = zoom;
  addLog('change', `缩放级别变化: ${zoom}`);
};

// 操作方法
const goToCity = (city: any) => {
  mapCenter.value = { lat: city.lat, lng: city.lng };
  addLog('action', `跳转到${city.name}`);
};

const addMultipleMarkers = () => {
  if (!mapRef.value) return;

  const markers = [
    { lat: 39.9042, lng: 116.4074, title: '天安门', content: '中华人民共和国的象征' },
    { lat: 39.9163, lng: 116.3972, title: '故宫', content: '明清两朝的皇家宫殿' },
    { lat: 39.8844, lng: 116.4056, title: '天坛', content: '明清皇帝祭天的场所' },
    { lat: 40.0031, lng: 116.3272, title: '颐和园', content: '中国古典园林的杰作' },
    { lat: 40.3598, lng: 116.0203, title: '八达岭长城', content: '万里长城的精华段' }
  ];

  markers.forEach(marker => {
    const markerId = mapRef.value!.addMarker({
      position: { lat: marker.lat, lng: marker.lng },
      title: marker.title,
      content: marker.content,
      draggable: true
    });

    // 为每个标记创建信息窗
    mapRef.value!.createInfoWindowForMarker(
      markerId,
      { lat: marker.lat, lng: marker.lng },
      `<div style="padding: 10px;">
        <h4 style="margin: 0 0 8px 0;">${marker.title}</h4>
        <p style="margin: 0; font-size: 14px;">${marker.content}</p>
      </div>`
    );
  });

  addLog('action', `添加了${markers.length}个标记`);
};

const createMarkerCluster = () => {
  if (!mapRef.value) return;

  // 在当前中心点周围创建一组标记
  const baseMarkers = [];
  for (let i = 0; i < 10; i++) {
    const lat = mapCenter.value.lat + (Math.random() - 0.5) * 0.02;
    const lng = mapCenter.value.lng + (Math.random() - 0.5) * 0.02;

    baseMarkers.push({
      position: { lat, lng },
      title: `集群标记 ${i + 1}`,
      content: `这是集群中的第${i + 1}个标记`,
      draggable: false
    });
  }

  mapRef.value.addMarkers(baseMarkers);
  addLog('action', `创建了包含${baseMarkers.length}个标记的集群`);
};

const showAllInfoWindows = () => {
  if (!mapRef.value) return;

  const markers = mapRef.value.markers || [];
  let count = 0;

  markers.forEach((marker: any) => {
    // 尝试显示标记关联的信息窗
    try {
      mapRef.value!.showMarkerInfoWindow(marker.id);
      count++;
    } catch (error) {
      // 如果没有关联的信息窗，忽略错误
    }
  });

  addLog('action', `显示了${count}个信息窗`);
};

const clearEverything = () => {
  if (!mapRef.value) return;

  const markerCount = mapRef.value.clearMarkers();
  const infoWindowCount = mapRef.value.clearInfoWindows();

  addLog('action', `清空了${markerCount}个标记和${infoWindowCount}个信息窗`);
};

const clearLogs = () => {
  eventLogs.value = [];
  addLog('system', '日志已清空');
};

// 监听地图配置变化
watch([mapType, sourceType, mapLanguage], () => {
  addLog('config', '地图配置已更新');
});
</script>

<style scoped>
.advanced-example {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.advanced-example h2 {
  color: #2c3e50;
  margin-bottom: 8px;
}

.advanced-example p {
  color: #7f8c8d;
  margin-bottom: 20px;
}

.example-layout {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 20px;
  margin-bottom: 20px;
}

@media (max-width: 1024px) {
  .example-layout {
    grid-template-columns: 1fr;
  }
}

.map-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.control-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section h3 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 14px;
}

.form-group {
  margin-bottom: 12px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.form-group select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
}

.preset-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.preset-btn {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: white;
  color: #495057;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.preset-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.batch-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.btn {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.btn.small {
  padding: 4px 8px;
  font-size: 11px;
}

.btn.primary {
  background: #007bff;
  color: white;
}

.btn.secondary {
  background: #6c757d;
  color: white;
}

.btn.info {
  background: #17a2b8;
  color: white;
}

.btn.danger {
  background: #dc3545;
  color: white;
}

.stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.stat-label {
  color: #6c757d;
}

.stat-value {
  color: #495057;
  font-weight: 500;
  font-family: monospace;
}

.event-log {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.event-log h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
}

.log-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6c757d;
  cursor: pointer;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 12px;
  background: #f8f9fa;
}

.log-entry {
  display: flex;
  gap: 12px;
  margin-bottom: 6px;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 3px;
}

.log-entry:last-child {
  margin-bottom: 0;
}

.log-entry.log-system {
  background: #e7f3ff;
}

.log-entry.log-click {
  background: #fff3cd;
}

.log-entry.log-marker {
  background: #d4edda;
}

.log-entry.log-change {
  background: #f8d7da;
}

.log-entry.log-action {
  background: #d1ecf1;
}

.log-entry.log-config {
  background: #e2e3e5;
}

.log-time {
  color: #6c757d;
  font-family: monospace;
  min-width: 80px;
}

.log-type {
  color: #495057;
  font-weight: 500;
  min-width: 60px;
  text-transform: uppercase;
}

.log-message {
  color: #495057;
  flex: 1;
}
</style>
