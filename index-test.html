<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>华为地图 Vue3 组件测试中心</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .test-center {
      background: white;
      border-radius: 12px;
      padding: 40px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      max-width: 600px;
      width: 90%;
    }
    
    .header {
      text-align: center;
      margin-bottom: 40px;
    }
    
    .header h1 {
      color: #333;
      font-size: 28px;
      margin-bottom: 10px;
    }
    
    .header p {
      color: #666;
      font-size: 16px;
    }
    
    .test-options {
      display: grid;
      gap: 20px;
    }
    
    .test-option {
      display: block;
      padding: 20px;
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      text-decoration: none;
      color: #333;
      transition: all 0.3s ease;
    }
    
    .test-option:hover {
      background: #e9ecef;
      border-color: #007bff;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    }
    
    .test-option h3 {
      margin: 0 0 8px 0;
      color: #007bff;
      font-size: 18px;
    }
    
    .test-option p {
      margin: 0;
      color: #666;
      font-size: 14px;
      line-height: 1.5;
    }
    
    .features {
      display: flex;
      gap: 8px;
      margin-top: 10px;
      flex-wrap: wrap;
    }
    
    .feature-tag {
      background: #007bff;
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
    }
    
    .footer {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #e9ecef;
      color: #666;
      font-size: 14px;
    }
    
    @media (max-width: 768px) {
      .test-center {
        padding: 30px 20px;
      }
      
      .header h1 {
        font-size: 24px;
      }
    }
  </style>
</head>
<body>
  <div class="test-center">
    <div class="header">
      <h1>华为地图 Vue3 组件测试中心</h1>
      <p>选择测试页面来验证组件功能</p>
    </div>
    
    <div class="test-options">
      <a href="/simple-test.html" class="test-option">
        <h3>🚀 简单测试</h3>
        <p>快速验证组件的基本功能，包括地图加载、标记添加、配置变更和事件捕获。</p>
        <div class="features">
          <span class="feature-tag">基础功能</span>
          <span class="feature-tag">快速验证</span>
          <span class="feature-tag">结果展示</span>
        </div>
      </a>
      
      <a href="/test.html" class="test-option">
        <h3>🔧 完整测试</h3>
        <p>全面测试所有组件功能，包括各种配置选项、事件监控、标记管理和信息窗功能。</p>
        <div class="features">
          <span class="feature-tag">完整功能</span>
          <span class="feature-tag">详细测试</span>
          <span class="feature-tag">集成测试</span>
        </div>
      </a>
      
      <a href="/advanced-test.html" class="test-option">
        <h3>🔍 高级功能测试 (v1.1.0)</h3>
        <p>测试最新的搜索和路径规划功能，包括地点搜索、地理编码、路径规划等高级特性。</p>
        <div class="features">
          <span class="feature-tag">搜索功能</span>
          <span class="feature-tag">路径规划</span>
          <span class="feature-tag">地理编码</span>
          <span class="feature-tag">v1.1.0</span>
        </div>
      </a>

      <a href="/autocomplete-test.html" class="test-option">
        <h3>🤖 自动补全测试</h3>
        <p>专门测试华为地图 HWAutocomplete 功能的集成，验证搜索建议和自动补全是否正常工作。</p>
        <div class="features">
          <span class="feature-tag">HWAutocomplete</span>
          <span class="feature-tag">搜索建议</span>
          <span class="feature-tag">调试工具</span>
        </div>
      </a>

      <a href="/" class="test-option">
        <h3>📱 演示应用</h3>
        <p>查看完整的演示应用，展示组件在实际项目中的使用效果。</p>
        <div class="features">
          <span class="feature-tag">实际应用</span>
          <span class="feature-tag">用户界面</span>
          <span class="feature-tag">完整体验</span>
        </div>
      </a>
    </div>
    
    <div class="footer">
      <p>华为地图 Vue3 组件库 v1.1.0</p>
      <p>请确保已配置华为地图 API 密钥</p>
    </div>
  </div>
</body>
</html>
