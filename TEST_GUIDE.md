# 华为地图 Vue3 组件测试指南

本指南将帮助您测试华为地图 Vue3 组件的完整性和功能。

## 🚀 快速开始

### 1. 配置环境

首先确保您已经配置了华为地图 API 密钥：

```bash
# 复制环境变量示例文件
cp .env.example .env

# 编辑 .env 文件，设置您的华为地图 API 密钥
# VITE_HUAWEI_MAP_API_KEY=your_actual_api_key_here
```

### 2. 启动测试服务器

```bash
# 安装依赖
npm install

# 启动测试服务器
npm run test:components
```

服务器启动后会自动打开浏览器，显示测试中心页面。

## 📋 测试页面说明

### 测试中心 (`/index-test.html`)
- 测试页面导航中心
- 选择不同的测试模式
- 查看测试说明和功能介绍

### 简单测试 (`/simple-test.html`)
**推荐用于快速验证组件基本功能**

#### 测试内容：
1. **基础地图测试**
   - 地图加载状态检查
   - 标记添加和清空功能
   - 地图中心点切换

2. **配置测试**
   - 实时配置修改
   - 地图参数调整
   - 控件显示切换

3. **事件测试**
   - 地图点击事件
   - 地图双击事件
   - 中心点和缩放变化事件

4. **测试结果**
   - 自动化测试结果显示
   - 功能通过/失败状态
   - 实时状态更新

### 完整测试 (`/test.html`)
**用于全面测试所有组件功能**

#### 测试模块：
1. **基础地图** - 不同配置的地图显示
2. **标记功能** - 标记的增删改查操作
3. **信息窗** - 信息窗的管理和显示
4. **事件监控** - 完整的事件捕获和日志
5. **配置面板** - 可视化配置管理
6. **集成测试** - 所有功能的综合测试

## 🔍 测试检查清单

### 基础功能测试
- [ ] 地图能正常加载显示
- [ ] 地图控件（缩放、比例尺等）正常工作
- [ ] 地图可以正常缩放和拖拽
- [ ] 地图中心点可以正确设置

### 标记功能测试
- [ ] 可以添加单个标记
- [ ] 可以批量添加标记
- [ ] 可以删除指定标记
- [ ] 可以清空所有标记
- [ ] 标记点击事件正常触发
- [ ] 标记信息正确显示

### 信息窗测试
- [ ] 可以创建信息窗
- [ ] 信息窗可以正确显示内容
- [ ] 信息窗可以正常打开和关闭
- [ ] 信息窗与标记的关联正确

### 事件系统测试
- [ ] 地图点击事件正常捕获
- [ ] 地图双击事件正常捕获
- [ ] 中心点变化事件正常触发
- [ ] 缩放级别变化事件正常触发
- [ ] 事件数据格式正确

### 配置系统测试
- [ ] 地图配置可以动态修改
- [ ] 配置变更能实时生效
- [ ] 控件显示/隐藏功能正常
- [ ] 地图样式切换正常

### 响应式测试
- [ ] 在不同屏幕尺寸下正常显示
- [ ] 移动设备上触摸操作正常
- [ ] 组件布局自适应正确

## 🐛 常见问题排查

### 地图无法加载
1. 检查 API 密钥是否正确配置
2. 检查网络连接是否正常
3. 查看浏览器控制台错误信息
4. 确认华为地图 API 服务可访问

### 功能异常
1. 查看浏览器控制台错误日志
2. 检查组件 props 传递是否正确
3. 确认事件监听器是否正确绑定
4. 验证数据格式是否符合要求

### 性能问题
1. 检查是否添加了过多标记
2. 确认事件监听器是否正确清理
3. 查看内存使用情况
4. 优化组件渲染频率

## 📊 测试报告

测试完成后，请记录以下信息：

### 环境信息
- 浏览器类型和版本
- 操作系统
- 屏幕分辨率
- 网络环境

### 测试结果
- 通过的功能项目
- 失败的功能项目
- 发现的问题和错误
- 性能表现评估

### 建议改进
- 功能改进建议
- 用户体验优化
- 性能优化建议
- 文档完善建议

## 🔧 开发者工具

### 调试模式
在测试过程中，您可以：
- 打开浏览器开发者工具查看详细日志
- 使用 Vue DevTools 检查组件状态
- 监控网络请求和 API 调用
- 分析性能和内存使用

### 自定义测试
如需添加自定义测试用例：
1. 在 `src/test-pages/` 目录下创建新的测试组件
2. 在测试配置中添加新的入口
3. 更新测试导航页面

## 📞 技术支持

如果在测试过程中遇到问题：
1. 查看项目 README.md 文档
2. 检查 GitHub Issues
3. 提交新的 Issue 报告问题
4. 联系项目维护者

---

**注意**: 测试前请确保已正确配置华为地图 API 密钥，否则地图无法正常加载。
