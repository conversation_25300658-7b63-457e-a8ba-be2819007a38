<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>华为地图自动补全测试</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f5f5;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .test-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #e9ecef;
      border-radius: 6px;
    }
    
    .test-section h3 {
      margin-top: 0;
      color: #333;
    }
    
    .search-input {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
      margin-bottom: 10px;
    }
    
    .search-input:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }
    
    .result {
      margin-top: 10px;
      padding: 10px;
      background: #f8f9fa;
      border-radius: 4px;
      font-family: monospace;
      font-size: 14px;
    }
    
    .error {
      color: #dc3545;
      background: #f8d7da;
      border: 1px solid #f5c6cb;
    }
    
    .success {
      color: #155724;
      background: #d4edda;
      border: 1px solid #c3e6cb;
    }
    
    .info {
      color: #0c5460;
      background: #d1ecf1;
      border: 1px solid #bee5eb;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>华为地图自动补全功能测试</h1>
    <p>这个页面用于测试华为地图的 HWAutocomplete 功能集成。</p>
    
    <div class="test-section">
      <h3>🔍 基础自动补全测试</h3>
      <p>在下面的输入框中输入地点名称，应该会显示自动补全建议：</p>
      <input 
        id="autocomplete-input" 
        type="text" 
        class="search-input" 
        placeholder="输入地点名称，如：北京天安门"
      />
      <div id="autocomplete-result" class="result info">
        等待输入...
      </div>
    </div>
    
    <div class="test-section">
      <h3>📍 地理编码测试</h3>
      <p>输入地址进行地理编码（地址转坐标）：</p>
      <input 
        id="geocode-input" 
        type="text" 
        class="search-input" 
        placeholder="输入完整地址"
      />
      <button onclick="testGeocode()">地理编码</button>
      <div id="geocode-result" class="result info">
        等待测试...
      </div>
    </div>
    
    <div class="test-section">
      <h3>🗺️ 地图状态</h3>
      <div id="map-status" class="result info">
        检查华为地图SDK加载状态...
      </div>
    </div>
  </div>

  <script type="module">
    import { HuaweiSearchService } from '/src/services/search-service.ts'
    import { loadHuaweiMapAPI } from '/src/utils/map-loader.ts'
    
    let searchService = null
    let autocompleteInstance = null
    
    // 检查地图SDK状态
    function checkMapStatus() {
      const statusEl = document.getElementById('map-status')
      
      if (window.HWMapJsSDK) {
        statusEl.innerHTML = '✅ 华为地图SDK已加载'
        statusEl.className = 'result success'
        return true
      } else {
        statusEl.innerHTML = '❌ 华为地图SDK未加载'
        statusEl.className = 'result error'
        return false
      }
    }
    
    // 初始化自动补全
    async function initAutocomplete() {
      try {
        // 加载华为地图API
        await loadHuaweiMapAPI()
        
        if (!checkMapStatus()) {
          return
        }
        
        // 创建搜索服务
        searchService = new HuaweiSearchService()
        
        // 获取输入框
        const inputElement = document.getElementById('autocomplete-input')
        const resultElement = document.getElementById('autocomplete-result')
        
        // 创建自动补全实例
        autocompleteInstance = searchService.createAutocomplete(inputElement, {
          language: 'zh-CN',
          location: { lat: 39.9042, lng: 116.4074 }, // 北京
          radius: 50000
        })
        
        if (autocompleteInstance) {
          resultElement.innerHTML = '✅ 自动补全已初始化，请在输入框中输入地点名称'
          resultElement.className = 'result success'
          
          // 监听选择事件
          autocompleteInstance.addListener('site_changed', () => {
            const site = autocompleteInstance.getSite()
            if (site) {
              resultElement.innerHTML = `🎯 选中地点：<br/>
                名称: ${site.name || '未知'}<br/>
                地址: ${site.formatAddress || '未知'}<br/>
                坐标: ${site.location?.lat || '未知'}, ${site.location?.lng || '未知'}<br/>
                类型: ${site.poi?.poiTypes?.[0] || '未知'}`
              resultElement.className = 'result success'
            }
          })
        } else {
          resultElement.innerHTML = '❌ 自动补全初始化失败'
          resultElement.className = 'result error'
        }
        
      } catch (error) {
        console.error('初始化失败:', error)
        document.getElementById('autocomplete-result').innerHTML = `❌ 初始化失败: ${error.message}`
        document.getElementById('autocomplete-result').className = 'result error'
      }
    }
    
    // 测试地理编码
    window.testGeocode = async function() {
      const input = document.getElementById('geocode-input').value.trim()
      const resultEl = document.getElementById('geocode-result')
      
      if (!input) {
        resultEl.innerHTML = '❌ 请输入地址'
        resultEl.className = 'result error'
        return
      }
      
      if (!searchService) {
        resultEl.innerHTML = '❌ 搜索服务未初始化'
        resultEl.className = 'result error'
        return
      }
      
      try {
        resultEl.innerHTML = '🔄 地理编码中...'
        resultEl.className = 'result info'
        
        const result = await searchService.geocode(input)
        
        resultEl.innerHTML = `✅ 地理编码成功：<br/>
          地址: ${result.address}<br/>
          坐标: ${result.location.lat}, ${result.location.lng}<br/>
          置信度: ${result.confidence}`
        resultEl.className = 'result success'
        
      } catch (error) {
        console.error('地理编码失败:', error)
        resultEl.innerHTML = `❌ 地理编码失败: ${error.message}`
        resultEl.className = 'result error'
      }
    }
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', () => {
      checkMapStatus()
      initAutocomplete()
    })
  </script>
</body>
</html>
