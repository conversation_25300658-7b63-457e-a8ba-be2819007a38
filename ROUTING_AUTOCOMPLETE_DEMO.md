# 路径规划中的 HWAutocomplete 功能演示

## 概述

本文档演示了如何在路径规划面板中使用 HWAutocomplete 组件，提供智能的起点、终点和途经点输入体验。

## 功能特性

### 1. 起点自动补全
- 输入起点地址时显示智能建议
- 基于地图中心位置提供相关建议
- 支持地点名称和地址搜索
- 自动填充坐标信息

### 2. 终点自动补全
- 输入终点地址时显示智能建议
- 与起点相同的智能搜索功能
- 支持从搜索结果直接设置终点

### 3. 途经点自动补全
- 动态添加途经点（最多5个）
- 每个途经点都有独立的自动补全功能
- 支持删除和重新排序
- 自动编号显示

## 使用演示

### 步骤1: 打开测试页面
访问 `http://localhost:5173/advanced-test.html`

### 步骤2: 展开路径规划面板
点击"路径规划"面板的"展开"按钮

### 步骤3: 测试起点自动补全
1. 在起点输入框中输入"天安门"
2. 观察自动补全建议列表
3. 选择一个建议项
4. 确认起点已设置

### 步骤4: 测试终点自动补全
1. 在终点输入框中输入"故宫"
2. 选择合适的建议项
3. 确认终点已设置

### 步骤5: 测试途经点功能
1. 点击"+ 添加途经点"按钮
2. 在新的途经点输入框中输入"王府井"
3. 选择建议项
4. 可以继续添加更多途经点

### 步骤6: 执行路径规划
1. 确保起点和终点都已设置
2. 选择路径类型（驾车、步行、骑行、公交）
3. 点击"开始规划"按钮
4. 查看规划结果

## 技术实现

### 组件集成
```vue
<HWAutocomplete
  v-model="originInput"
  placeholder="输入起点地址或点击地图选择"
  input-class="waypoint-input"
  :location="mapCenter"
  :radius="10000"
  poi-type="GEOCODE"
  @site-selected="onOriginSelected"
  @enter="geocodeOrigin"
/>
```

### 事件处理
```javascript
const onOriginSelected = (site, result) => {
  origin.value = result.location
  originInput.value = result.name || result.address
}
```

### 途经点管理
```javascript
const onWaypointSelected = (index, site, result) => {
  if (waypoints.value[index]) {
    waypoints.value[index].location = result.location
    waypoints.value[index].address = result.name || result.address
  }
}
```

## 配置选项

### POI类型设置
- `GEOCODE`: 返回地理编码结果（推荐用于路径规划）
- `ESTABLISHMENT`: 返回商业结果
- `ADDRESS`: 仅返回精确地址

### 位置偏向
- 基于地图中心位置提供相关建议
- 搜索半径默认为10公里
- 可根据需要调整搜索范围

## 错误处理

### 自动补全不可用时
- 自动降级到手动地理编码
- 显示相应的状态提示
- 保持基本功能可用

### 网络错误处理
- 显示错误信息
- 提供重试机制
- 不影响其他功能

## 最佳实践

### 1. 用户体验优化
- 提供清晰的占位符文本
- 显示加载状态和错误信息
- 支持键盘导航

### 2. 性能优化
- 使用防抖机制减少API调用
- 缓存常用搜索结果
- 优化组件渲染

### 3. 可访问性
- 支持屏幕阅读器
- 提供键盘快捷键
- 确保颜色对比度

## 故障排除

### 常见问题

1. **自动补全不显示**
   - 检查华为地图SDK是否加载
   - 确认API密钥配置正确
   - 查看浏览器控制台错误信息

2. **选择建议后没有反应**
   - 检查事件处理函数是否正确绑定
   - 确认数据格式是否正确
   - 查看组件状态更新

3. **途经点无法添加**
   - 确认途经点数量未超过限制（5个）
   - 检查数组操作是否正确
   - 验证响应式数据绑定

### 调试技巧

1. **开启控制台日志**
   ```javascript
   console.log('自动补全选中的地点:', site, result)
   ```

2. **检查组件状态**
   ```javascript
   console.log('当前起点:', origin.value)
   console.log('当前终点:', destination.value)
   console.log('途经点列表:', waypoints.value)
   ```

3. **验证API响应**
   ```javascript
   console.log('华为地图SDK状态:', window.HWMapJsSDK)
   ```

## 总结

通过集成 HWAutocomplete 组件，路径规划功能现在提供了更智能和用户友好的输入体验。用户可以快速输入起点、终点和途经点，大大提高了路径规划的效率和准确性。

这个实现展示了如何将华为地图的自动补全功能与Vue3组件完美结合，为用户提供现代化的地图应用体验。
