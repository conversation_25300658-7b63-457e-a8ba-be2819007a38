# 📦 发布指南

## 快速发布

### 方法一：使用发布脚本（推荐）

```bash
# 运行发布脚本
./scripts/publish.sh
```

脚本会自动完成以下步骤：
1. 检查NPM登录状态
2. 检查包名可用性
3. 运行测试和代码检查
4. 构建项目
5. 发布到NPM
6. 可选创建Git标签

### 方法二：手动发布

```bash
# 1. 登录NPM（如果还没登录）
npm login

# 2. 检查包名是否可用
npm view huawei-map-vue3

# 3. 运行测试
npm run test:unit

# 4. 代码检查
npm run lint

# 5. 构建项目
npm run build

# 6. 发布
npm publish
```

## 发布前检查清单

- [ ] 已登录NPM账号
- [ ] 包名可用或版本号正确
- [ ] 所有测试通过
- [ ] 代码检查通过
- [ ] 构建成功
- [ ] README.md 文档完整
- [ ] CHANGELOG.md 已更新
- [ ] package.json 信息正确

## 版本管理

### 更新版本号

```bash
# 补丁版本 (1.0.0 -> 1.0.1)
npm version patch

# 次要版本 (1.0.0 -> 1.1.0)
npm version minor

# 主要版本 (1.0.0 -> 2.0.0)
npm version major
```

### 发布测试版本

```bash
# 发布beta版本
npm version prerelease --preid=beta
npm publish --tag beta

# 安装beta版本
npm install huawei-map-vue3@beta
```

## 发布后验证

1. 检查NPM包页面：https://www.npmjs.com/package/huawei-map-vue3
2. 测试安装：`npm install huawei-map-vue3`
3. 验证类型定义：检查IDE中的类型提示
4. 测试导入：`import { HuaweiMap } from 'huawei-map-vue3'`

## 常见问题

### 1. 登录问题
```bash
# 如果登录失败，尝试清除缓存
npm logout
npm login
```

### 2. 包名冲突
如果包名已存在，可以：
- 使用作用域包名：`@your-username/huawei-map-vue3`
- 选择其他包名

### 3. 权限问题
确保你有发布权限：
```bash
npm whoami
npm access list packages
```

### 4. 网络问题
如果发布失败，可能是网络问题：
```bash
# 检查NPM配置
npm config get registry

# 如果使用了淘宝镜像，切换回官方源
npm config set registry https://registry.npmjs.org/
```

## 发布成功后

1. 🎉 恭喜！你的包已发布到NPM
2. 📦 包地址：https://www.npmjs.com/package/huawei-map-vue3
3. 📚 用户可以通过 `npm install huawei-map-vue3` 安装
4. 🔄 记得在GitHub上创建Release和标签
5. 📢 可以在社区分享你的组件

## 维护建议

1. **定期更新**：修复bug和添加新功能
2. **版本管理**：遵循语义化版本规范
3. **文档维护**：保持README和API文档更新
4. **社区支持**：及时回复issues和PR
5. **安全更新**：定期更新依赖包
