<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>华为地图SDK加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background: #f0f9ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #1890ff;
            color: white;
        }
        button:hover {
            background: #40a9ff;
        }
        #map {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>华为地图SDK加载测试</h1>
    
    <div class="test-section">
        <h3>1. 环境变量检查</h3>
        <div id="env-check"></div>
    </div>
    
    <div class="test-section">
        <h3>2. SDK加载状态</h3>
        <div id="sdk-status"></div>
        <button onclick="checkSDK()">检查SDK状态</button>
        <button onclick="loadSDK()">手动加载SDK</button>
    </div>
    
    <div class="test-section">
        <h3>3. 地图创建测试</h3>
        <div id="map-status"></div>
        <button onclick="createMap()">创建地图</button>
        <div id="map"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 自动补全测试</h3>
        <div id="autocomplete-status"></div>
        <input type="text" id="test-input" placeholder="输入地址测试自动补全" style="width: 300px; padding: 8px;">
        <button onclick="testAutocomplete()">测试自动补全</button>
    </div>
    
    <div class="test-section">
        <h3>5. 路径规划测试</h3>
        <div id="routing-status"></div>
        <button onclick="testRouting()">测试路径规划</button>
    </div>

    <script type="module">
        // 检查环境变量
        function checkEnv() {
            const envDiv = document.getElementById('env-check')
            const apiKey = import.meta.env.VITE_HUAWEI_MAP_API_KEY
            const apiUrl = import.meta.env.VITE_HUAWEI_MAP_API_URL
            
            let html = ''
            if (apiKey) {
                html += `<div class="result success">✅ API密钥已配置: ${apiKey.substring(0, 20)}...</div>`
            } else {
                html += `<div class="result error">❌ API密钥未配置</div>`
            }
            
            if (apiUrl) {
                html += `<div class="result success">✅ API地址已配置: ${apiUrl}</div>`
            } else {
                html += `<div class="result error">❌ API地址未配置</div>`
            }
            
            envDiv.innerHTML = html
        }
        
        // 检查SDK状态
        window.checkSDK = function() {
            const statusDiv = document.getElementById('sdk-status')
            
            if (window.HWMapJsSDK) {
                statusDiv.innerHTML = `
                    <div class="result success">✅ 华为地图SDK已加载</div>
                    <div class="result info">SDK对象: ${Object.keys(window.HWMapJsSDK).join(', ')}</div>
                `
            } else {
                statusDiv.innerHTML = `<div class="result error">❌ 华为地图SDK未加载</div>`
            }
        }
        
        // 手动加载SDK
        window.loadSDK = async function() {
            const statusDiv = document.getElementById('sdk-status')
            statusDiv.innerHTML = `<div class="result info">⏳ 正在加载华为地图SDK...</div>`
            
            try {
                const { loadHuaweiMapAPI } = await import('/src/utils/map-loader.ts')
                await loadHuaweiMapAPI()
                checkSDK()
            } catch (error) {
                statusDiv.innerHTML = `<div class="result error">❌ SDK加载失败: ${error.message}</div>`
            }
        }
        
        // 创建地图
        window.createMap = function() {
            const statusDiv = document.getElementById('map-status')
            const mapDiv = document.getElementById('map')
            
            if (!window.HWMapJsSDK) {
                statusDiv.innerHTML = `<div class="result error">❌ 请先加载华为地图SDK</div>`
                return
            }
            
            try {
                const map = new window.HWMapJsSDK.HWMap(mapDiv, {
                    center: { lat: 39.9042, lng: 116.4074 },
                    zoom: 10
                })
                
                statusDiv.innerHTML = `<div class="result success">✅ 地图创建成功</div>`
                
                // 添加点击事件测试
                map.on('click', (event) => {
                    console.log('地图点击:', event)
                    statusDiv.innerHTML = `<div class="result success">✅ 地图交互正常 - 点击坐标: ${event.latLng.lat.toFixed(6)}, ${event.latLng.lng.toFixed(6)}</div>`
                })
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="result error">❌ 地图创建失败: ${error.message}</div>`
            }
        }
        
        // 测试自动补全
        window.testAutocomplete = function() {
            const statusDiv = document.getElementById('autocomplete-status')
            const input = document.getElementById('test-input')
            
            if (!window.HWMapJsSDK) {
                statusDiv.innerHTML = `<div class="result error">❌ 请先加载华为地图SDK</div>`
                return
            }
            
            try {
                const autocomplete = new window.HWMapJsSDK.HWAutocomplete(input, {
                    language: 'zh-CN',
                    location: { lat: 39.9042, lng: 116.4074 },
                    radius: 50000
                })
                
                autocomplete.addListener('site_changed', () => {
                    const site = autocomplete.getSite()
                    if (site) {
                        statusDiv.innerHTML = `<div class="result success">✅ 自动补全工作正常 - 选择了: ${site.name}</div>`
                    }
                })
                
                statusDiv.innerHTML = `<div class="result success">✅ 自动补全初始化成功</div>`
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="result error">❌ 自动补全初始化失败: ${error.message}</div>`
            }
        }
        
        // 测试路径规划
        window.testRouting = function() {
            const statusDiv = document.getElementById('routing-status')
            
            if (!window.HWMapJsSDK) {
                statusDiv.innerHTML = `<div class="result error">❌ 请先加载华为地图SDK</div>`
                return
            }
            
            try {
                const directionsService = new window.HWMapJsSDK.HWDirectionsService()
                
                const request = {
                    origin: { lat: 39.9042, lng: 116.4074 }, // 天安门
                    destination: { lat: 39.9163, lng: 116.3972 } // 故宫
                }
                
                statusDiv.innerHTML = `<div class="result info">⏳ 正在规划路径...</div>`
                
                directionsService.routeDriving(request, (result, status) => {
                    if (status === '0' || status === 'OK') {
                        statusDiv.innerHTML = `<div class="result success">✅ 路径规划成功 - 距离: ${result.routes[0].legs[0].distance.text}</div>`
                    } else {
                        statusDiv.innerHTML = `<div class="result error">❌ 路径规划失败 - 状态: ${status}</div>`
                    }
                })
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="result error">❌ 路径规划服务初始化失败: ${error.message}</div>`
            }
        }
        
        // 页面加载时检查环境
        checkEnv()
        checkSDK()
        
        // 自动尝试加载SDK
        setTimeout(() => {
            if (!window.HWMapJsSDK) {
                loadSDK()
            }
        }, 1000)
    </script>
</body>
</html>
