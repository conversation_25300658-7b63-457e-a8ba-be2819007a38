<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>华为地图 Vue3 组件 - 高级功能测试 (v1.1.0)</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
      min-height: 100vh;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px 0;
      text-align: center;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .header h1 {
      font-size: 28px;
      margin-bottom: 8px;
    }

    .header p {
      font-size: 16px;
      opacity: 0.9;
    }

    .nav-bar {
      background: white;
      padding: 15px 0;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    .nav-bar a {
      display: inline-block;
      margin: 0 15px;
      padding: 8px 16px;
      background: #007bff;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      transition: background 0.3s ease;
    }

    .nav-bar a:hover {
      background: #0056b3;
    }

    .nav-bar a.back {
      background: #6c757d;
    }

    .nav-bar a.back:hover {
      background: #545b62;
    }

    .container {
      max-width: 1400px;
      margin: 20px auto;
      padding: 0 20px;
    }

    .features-info {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .features-info h2 {
      color: #333;
      margin-bottom: 15px;
      font-size: 20px;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
    }

    .feature-card {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 15px;
    }

    .feature-card h3 {
      color: #007bff;
      margin-bottom: 8px;
      font-size: 16px;
    }

    .feature-card p {
      color: #666;
      font-size: 14px;
      line-height: 1.4;
    }

    .feature-list {
      list-style: none;
      margin-top: 10px;
    }

    .feature-list li {
      color: #666;
      font-size: 13px;
      margin-bottom: 4px;
      padding-left: 16px;
      position: relative;
    }

    .feature-list li:before {
      content: "✓";
      color: #28a745;
      font-weight: bold;
      position: absolute;
      left: 0;
    }

    .app-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      min-height: 600px;
    }

    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 400px;
      color: #666;
      font-size: 16px;
    }

    .loading:before {
      content: "";
      width: 20px;
      height: 20px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #007bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 10px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .footer {
      text-align: center;
      padding: 20px;
      color: #666;
      font-size: 14px;
    }

    @media (max-width: 768px) {
      .container {
        padding: 0 10px;
      }

      .features-grid {
        grid-template-columns: 1fr;
      }

      .header h1 {
        font-size: 24px;
      }

      .nav-bar a {
        margin: 5px;
        font-size: 14px;
      }
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>华为地图 Vue3 组件 - 高级功能测试</h1>
    <p>v1.1.0 - 搜索与路径规划功能</p>
  </div>

  <div class="nav-bar">
    <a href="/index-test.html" class="back">← 返回测试中心</a>
    <a href="/simple-test.html">简单测试</a>
    <a href="/test.html">完整测试</a>
    <a href="/routing-test.html">路径规划测试</a>
    <a href="/">演示应用</a>
  </div>

  <div class="container">
    <div class="features-info">
      <h2>🚀 v1.1.0 新功能特性</h2>
      <div class="features-grid">
        <div class="feature-card">
          <h3>🔍 位置搜索功能</h3>
          <p>强大的地点搜索和地理编码功能</p>
          <ul class="feature-list">
            <li>地点搜索：根据关键词搜索地点、POI</li>
            <li>地理编码：地址转坐标 (Geocoding)</li>
            <li>逆地理编码：坐标转地址</li>
            <li>周边搜索：搜索指定位置周边的POI</li>
            <li>搜索建议：实时搜索建议和自动补全</li>
            <li>搜索历史：保存和管理搜索历史记录</li>
          </ul>
        </div>

        <div class="feature-card">
          <h3>🛣️ 路径规划功能</h3>
          <p>多种出行方式的路径规划服务</p>
          <ul class="feature-list">
            <li>驾车路径：支持多种驾车策略</li>
            <li>步行路径：步行导航路径规划</li>
            <li>骑行路径：自行车路径规划</li>
            <li>多点路径：支持途经点的路径规划</li>
            <li>路径展示：在地图上绘制路径线条</li>
            <li>路径统计：距离、时间等信息统计</li>
          </ul>
        </div>

        <div class="feature-card">
          <h3>🎨 新增UI组件</h3>
          <p>专业的搜索和路径规划界面</p>
          <ul class="feature-list">
            <li>SearchPanel - 搜索面板组件</li>
            <li>RoutingPanel - 路径规划面板组件</li>
            <li>响应式设计，适配移动端</li>
            <li>实时状态反馈</li>
            <li>错误处理和用户提示</li>
            <li>可自定义样式和配置</li>
          </ul>
        </div>

        <div class="feature-card">
          <h3>🔧 开发者工具</h3>
          <p>便于开发和集成的工具和API</p>
          <ul class="feature-list">
            <li>useSearch - 搜索功能组合式函数</li>
            <li>useRouting - 路径规划组合式函数</li>
            <li>TypeScript 完整类型支持</li>
            <li>详细的API文档和示例</li>
            <li>事件监听和状态管理</li>
            <li>错误处理和调试支持</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="app-container">
      <div id="advanced-test-app">
        <div class="loading">
          正在加载高级功能测试页面...
        </div>
      </div>
    </div>
  </div>

  <div class="footer">
    <p>华为地图 Vue3 组件库 v1.1.0 - 高级功能测试</p>
    <p>请确保已在 .env 文件中配置华为地图 API 密钥 (VITE_HUAWEI_MAP_API_KEY)</p>
  </div>

  <script type="module" src="/src/test-pages/advanced-main.ts"></script>
</body>
</html>
