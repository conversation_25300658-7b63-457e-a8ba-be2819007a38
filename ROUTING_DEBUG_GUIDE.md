# 路径规划功能调试指南

## 问题诊断

### 1. 路径规划点击无反应的可能原因

1. **华为地图SDK未加载**
   - 检查浏览器控制台是否有 `window.HWMapJsSDK` 对象
   - 确认API密钥配置正确

2. **地图实例未正确传递**
   - 检查 RoutingPanel 组件是否接收到地图实例
   - 确认 useRouting 是否正确初始化

3. **起点终点未设置**
   - 确认起点和终点坐标已正确设置
   - 检查自动补全是否正常工作

### 2. 文字显示不清楚的问题

已修复的样式问题：
- 将关键文字颜色从 `#666` 改为 `#333`
- 增加了字体粗细 `font-weight: 500`
- 改善了对比度

## 调试步骤

### 步骤1: 检查SDK加载状态

在浏览器控制台执行：
```javascript
console.log('华为地图SDK:', !!window.HWMapJsSDK)
console.log('SDK对象:', window.HWMapJsSDK)
```

### 步骤2: 检查地图实例

在浏览器控制台执行：
```javascript
// 查看地图实例
console.log('地图实例:', window.mapInstance)
```

### 步骤3: 手动测试路径规划

在浏览器控制台执行：
```javascript
// 手动测试路径规划
if (window.HWMapJsSDK) {
  const directionsService = new window.HWMapJsSDK.HWDirectionsService()
  const request = {
    origin: { lat: 39.9042, lng: 116.4074 },
    destination: { lat: 39.9163, lng: 116.3972 }
  }
  
  directionsService.routeDriving(request, (result, status) => {
    console.log('路径规划结果:', result, status)
  })
}
```

### 步骤4: 检查自动补全功能

1. 在起点输入框输入"天安门"
2. 观察是否显示自动补全建议
3. 选择一个建议项
4. 检查控制台是否有相关日志

### 步骤5: 检查路径规划按钮

1. 确保起点和终点都已设置
2. 点击"开始规划"按钮
3. 观察控制台日志输出
4. 检查是否有错误信息

## 常见错误及解决方案

### 错误1: "华为地图SDK未加载"

**解决方案**：
1. 检查 `.env` 文件中的 `VITE_HUAWEI_MAP_API_KEY` 配置
2. 确认网络连接正常
3. 检查API密钥是否有效

### 错误2: "地图实例未设置"

**解决方案**：
1. 确认 HuaweiMap 组件正确触发了 `map-ready` 事件
2. 检查 RoutingPanel 组件是否接收到 `map` prop
3. 验证 useRouting 的地图实例设置

### 错误3: "起点或终点未设置"

**解决方案**：
1. 使用快速测试按钮设置预设路线
2. 手动在输入框中输入地址并选择自动补全建议
3. 检查坐标是否正确设置

### 错误4: 路径规划API调用失败

**解决方案**：
1. 检查API密钥权限
2. 确认起点终点坐标格式正确
3. 检查网络连接和API服务状态

## 测试流程

### 完整测试流程

1. **打开测试页面**
   ```
   http://localhost:5174/routing-test.html
   ```

2. **检查初始状态**
   - 地图正常显示
   - 路径规划面板展开
   - 控制台无错误信息

3. **测试自动补全**
   - 在起点输入框输入"天安门"
   - 选择自动补全建议
   - 在终点输入框输入"故宫"
   - 选择自动补全建议

4. **测试路径规划**
   - 点击"开始规划"按钮
   - 观察按钮状态变化（规划中...）
   - 检查地图上是否显示路径
   - 查看路径详情信息

5. **测试快速功能**
   - 点击"🏛️ 北京路线"按钮
   - 观察地图标记和路径
   - 测试"清除"功能

## 预期结果

### 正常工作的标志

1. **自动补全正常**
   - 输入时显示建议列表
   - 选择建议后自动填充地址
   - 坐标正确设置

2. **路径规划正常**
   - 点击规划按钮后状态变为"规划中..."
   - 几秒后显示路径结果
   - 地图上显示路径线条
   - 显示距离和时间信息

3. **界面显示正常**
   - 文字清晰可读
   - 按钮状态正确
   - 布局完整无遮挡

## 故障排除

如果路径规划仍然不工作：

1. **重新加载页面**
2. **清除浏览器缓存**
3. **检查网络连接**
4. **验证API密钥配置**
5. **查看浏览器控制台错误信息**

## 联系支持

如果问题持续存在，请提供：
1. 浏览器控制台的完整错误日志
2. 网络请求的详细信息
3. 使用的浏览器版本和操作系统
4. API密钥配置状态（不要包含实际密钥）
