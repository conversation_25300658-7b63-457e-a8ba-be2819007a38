# 官方API

## 基础例子

```html
<!DOCTYPE html>
<head>
    <meta charset="UTF-8">
    <style>
        #map {
            height: 800px;
            width: 100%;
            margin: 0 auto;
        }
    </style>
</head>
<script src="https://mapapi.cloud.huawei.com/mapjs/v1/api/js?callback=initMap&key=API_KEY"></script>
<body>
    <div id="map"></div>
</body>
<script>
    function initMap() {
        var mapOptions = {};
        // 设置地图中心点坐标
        mapOptions.center = {lat:23.130415047434678, lng:113.32380294799805};
        // 设置地图初始化缩放级别
        mapOptions.zoom = 8;
        // 设置地图语言
        mapOptions.language='ENG';
        // 设置地图加载时使用的瓦片类型，支持vector（矢量）或raster（栅格）
        mapOptions.sourceType = 'raster';
        // 创建地图对象
        var map = new HWMapJsSDK.HWMap(document.getElementById('map'), mapOptions);
    }
</script>
</html>
```

## HWMap

### 概述

API中的核心类，用来加载一张地图。

### 构造函数

|函数|描述|
|---|---|
| HWMapJsSDK.HWMap(div, mapOptions)|div：填充地图容器。mapOptions：设置地图属性，详情见MapOptions。|

### 方法

|方法 | 描述 |参数类型| 返回值 |
|--|--|--|--|
|fitBounds(bounds)|设置地图显示区域范围。|LatLngBounds|-|
|fromScreenLocation(pixel)|屏幕坐标转经纬度。说明地图加载完成后使用。|{x, y}x：Number，横坐标偏移的像素数。y：Number，纵坐标偏移的像素数。|LatLng|
|getBounds()|获取地图显示区域。|-|LatLngBounds|
|getCenter()|获取地图中心点坐标。|-|LatLng|
|getDiv()|获取装载地图的容器。|-|div容器。|
|getHeading()|获取地图朝向。|-|Number表示地图朝向与正北夹角值。|
|getMapType()|获取地图类型。|-|String|
|getOpacity()|获取地图的不透明度。|-|Number|
|getPoi(pixel)|获取当前位置的POI数据。|[Number, Number]表示地图上一个像素点坐标，分别表示横坐标和纵坐标。|返回当前点击位置的POI信息，包含位置和属性。|
|getSize()|获取地图大小。|-|返回|[|width, height]两个Number类型的数组。
|getZoom()|获取地图当前缩放级别。|-|Number|
|panBy(x, y)|将地图中心点偏移|(x, y)。|x：Number，横坐标偏移的像素数。y：Number，纵坐标偏移的像素数。|-|
|panTo(latLng)|地图中心移动到指定坐标点。|LatLng|-|
|panToBounds(latLngBounds)|移动地图使得包含指定坐标区域。|[lng1, lat1, lng2, lat2]|-|
|resize()|重新计算地图容器大小。|-|-|
|setCenter(latlng)|设置地图中心点坐标。|LatLng|-|
|setCopyrightControl(enabled)|设置是否显示版权。|Booleantrue：显示false：关闭|-|
|setFitView(points, options?)|根据输入的经纬度数组计算最优视角。|points: Array<LatLng>options?: FitViewOptions|-|
|setHeading(heading)|设置地图朝向。|Number值为地图朝向与实际正北的夹角，取值范围：[0, 360]。|-|
|setLocationControl(enabled)|设置是否显示当前位置按钮。|Booleantrue：显示false：关闭|-|
|setLogoPosition(logoPosition)|设置Petal Maps Logo位置。|StringBOTTOM_LEFT：左下BOTTOM_RIGHT：右下TOP_LEFT：左上TOP_RIGHT：右上默认为BOTTOM_LEFT。|-|
|setMapType(mapType)|设置地图类型。|StringROADMAP：基础地图TERRAIN：地形图|-|
|setNavigationControl(enabled)|设置是否显示平移按钮。|Booleantrue：显示false：关闭|-|
|setOpacity(opacity)|设置地图不透明度。|Number取值范围：[0, 1]。|-|
|setPinchRotate(disabled)|设置地图双指旋转是否可用。|Boolean，true：可用false：不可用默认值为true。|-|
|setPresetStyleId(presetStyleId)|设置预置地图样式。|String可选值为：standard、night、simple。|-|
|setPreviewId(previewId)|设置预览ID，通过预览ID设置自定义地图样式。|String预览ID。|-|
|setRotateControl(enabled)|设置是否显示指北针。|Booleantrue：显示false：关闭|-|
|setScaleControl(enabled)|设置是否显示比例尺。|Booleantrue：显示false：关闭|-|
|setStyle(styles)|通过JSON文件设置自定义地图样式。|JSON数组。|-|
|setStyleId(styleId)|设置样式ID，通过样式ID设置自定义地图样式。|String样式ID。|-|
|setTitle(title)|设置地图所在容器的提示信息。|String|-|
|setZoom(zoom)|设置地图缩放级别。|数值类型取值范围：[minZoom, maxZoom]。|-|
|setZoomControl(enabled)|设置是否显示缩放按钮。|Booleantrue：显示false：关闭|-|
|setZoomSlider(enabled)|设置是否显示缩放条。|Booleantrue：显示false：关闭|-|
|toScreenLocation(latlng)|经纬度转屏幕坐标。说明地图加载完成后使用。|LatLng|{x, y}x：Number，横坐标偏移的像素数。y：Number，纵坐标偏移的像素数。|
|zoomIn()|地图放大一个级别。|-|-|
|zoomOut()|地图缩小一个级别。|-|-|
|on(event, callback)|设置事件触发时调用的自定义函数。|event：String，事件类型，见事件。callback：Function，事件触发的回调函数。|-|
|onCenterChanged(callback)|地图中心点改变。|Function自定义回调函数。|-|
|onHeadingChanged(callback)|地图方向改变。|Function自定义回调函数。|-|
|onZoomChanged(callback)|缩放级别改变。|Function自定义回调函数。|-|
|un(event, callback)|解绑事件侦听。|event：String，事件类型，见事件。callback：Function，事件触发的回调函数。|-|

### 事件

|事件|描述|用法|说明|
|--|--|--|--|
|click|鼠标左键点击，与dblclick会冲突。|map.on('click', callback)|callback回调函数中，参数event对象包含了两个重要的参数：coordinate：墨卡托3857投影经纬度。可以使用HWMapJsSDK.HWMapUtils.epsgToLatLng(event.coordinate)方法，将墨卡托3857投影经纬度转换成日常使用经纬度。pixel：屏幕坐标，当前交互点与屏幕左上角的像素距离。|
|contextmenu|鼠标右键点击。|map.on('contextmenu', callback)|
|dblclick|鼠标左键双击。|map.on('dblclick', callback)|
|singleclick|鼠标左键点击，此事件会延迟250毫秒，以确保它不是双击事件，与dblclick不冲突。|map.on('singleclick', callback)|
|pointermove|鼠标移动。|map.on('pointermove', callback)|
|pointerdown|鼠标键按下。|map.on('pointerdown', callback)|
|pointerup|鼠标键松开。|map.on('pointerup', callback)|
|pointerdrag|地图拖动。|map.on('pointerdrag', callback)|
|movestart|地图开始移动。|map.on('movestart', callback)|-|
|moveend|地图结束移动。|map.on('moveend', callback)|-|
|onCenterChanged|地图中心点改变。|map.onCenterChanged(callback)|-|
|onHeadingChanged|方向改变。|map.onHeadingChanged(callback)|-|
|onZoomChanged|缩放级别改变。|map.onZoomChanged(callback)|-|

### AuthOptions

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|accessToken|是|String|AT。|

### CopyrightControlOptions

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|value|是|String|目前支持文本或HTML的dom元素，dom元素支持以下标签：<font>标签<a>标签<img>标签|

### MapOptions

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|authOptions|否|AuthOptions|设置AT。|
|center|是|LatLng|地图中心点。|
|copyrightControl|否|Boolean|设置地图是否显示版权，默认false。|
|copyrightControlOptions|否|CopyrightControlOptions|设置地图的版权内容。|
|language|否|String|设置地图语言，取值范围参见LanguageCode。说明推荐使用BCP 47语言码。|
|locationControl|否|Boolean|设置地图是否显示当前位置按钮，默认false。|
|logoPosition|否|String|设置Petal Maps Logo位置。取值包括：BOTTOM_LEFT：左下BOTTOM_RIGHT：右下TOP_LEFT：左上TOP_RIGHT：右上默认为BOTTOM_LEFT。|
|mapType|否|String|设置地图类型。取值包括：ROADMAP：基础地图TERRAIN：地形图|
|maxZoom|否|Number|地图最大缩放级别。取值范围：[2, 20]，默认20。|
|minZoom|否|Number|地图最小缩放级别。取值范围：[2, 20]，默认2。|
|navigationControl|否|Boolean|设置地图是否显示平移按钮，默认false。|
|presetStyleId|否|String|设置预置地图样式。可选值为：standard、night、simple。|
|rotateControl|否|Boolean|设置地图是否显示指北针，默认false。|
|scaleControl|否|Boolean|设置地图是否显示比例尺，默认false。|
|sourceType|否|String|设置地图加载时使用的瓦片类型，支持vector（矢量）或raster（栅格），默认为vector。|
|zoom|是|Number|地图初始化时缩放级别。|
|zoomSlider|否|Boolean|设置地图是否显示缩放条，默认false。|
|zoomControl|否|Boolean|设置地图是否显示缩放按钮，默认true。|
|rasterPreload|否|Boolean|设置矢量图是否支持栅格预加载，默认true。|

### ScaleControlOptions

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|units|否|String|比例尺单位。取值包括：imperial（英制英寸）、nautical（海里）、metric（公制），默认为metric。|

### LanguageCode

|ISO-639-2|BCP 47|中文名称|
|--|--|--|
|CHI|zh|中文简体|
|ZHO|zh-Hant|中文繁体|

## HWCustomPoi

### 概述

在地图上添加支持碰撞的文字标注。

### 构造函数

|函数|描述|
|--|--|
|HWMapJsSDK.HWCustomPoi(customPoiOptions)|customPoiOptions：设置文字标注的属性，详情见CustomPoiOptions。|

### 方法

|方法|描述|参数类型|返回值|
|--|--|--|--|
|getCollisionEnabled()|获取是否与地图元素碰撞标识。|-|Boolean|
|getIcon()|获取Icon。|-|String | CustomPoiIconOption|
|getLabel()|获取Label。|-|String | CustomPoiLabelOption|
|getMap()|获取绑定的地图实例。|-|HWMap|
|getPosition()|获取标记的位置。|-|LatLng|
|getVariableLabelAnchor()|获取用于文字展示的方位。|-|Array<String>|
|getZIndex()|获取z-index属性。|-|Number|
|setCollisionEnabled()|设置是否与地图元素碰撞。|Boolean|-|
|setIcon(icon)|设置Icon。|String | CustomPoiIconOption|-|
|setLabel(label)|设置Label。|String | CustomPoiLabelOption|-|
|setMap(map)|绑定到地图。|HWMap|-|
|setPosition(position)|设置标记的位置。|LatLng|-|
|setVariableLabelAnchor()|设置文字展示的方位。|Array<String>|-|
|setZIndex(zIndex)|设置z-index属性，CustomPoi的压盖和碰撞关系。|Number|-|
|addListener(type, callback)|添加事件侦听。|type：String，见事件。callback：Function，事件触发的回调函数。|-|
|removeListener(type, callback)|移除事件侦听。|type：String，见事件。callback：Function，事件触发的回调函数。|-|

### 事件

|事件|描述|用法|
|--|--|--|
|click|鼠标单击。|naviLine.addListener('click', callback)，参数callback为自定义回调函数。|

### CustomPoiOptions

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|collisionEnabled|否|Boolean|是否参与碰撞。true：参与碰撞，碰撞优先级高于底图元素。多个CustomPoi之间碰撞关系受zIndex影响。false：不参与碰撞，直接压盖在地图元素之上显示。默认值为true。|
|icon|否|String | CustomPoiIconOption|标记的图标，可以是图片的url路径或文件路径。说明请确保url允许跨域访问。|
|label|否|String | CustomPoiLabelOption|标记的标签，文字或者详细信息。|
|map|是|HWMap|地图实例。|
|position|是|LatLng|标记的位置。|
|variableLabelAnchor|否|Array<String>|设置多个用于文字展示的方位。可选方位：topbottomrightleft说明当collisionEnable设置为true时生效。|
|zIndex|否|Number|CustomPoi的压盖和碰撞关系。取值范围：大于等于0的整数，默认0。zIndex大的优先于zIndex小的，对于开启碰撞和关闭碰撞之间的压盖，非碰撞的压盖碰撞的。|

### CustomPoiIconOption

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|anchor|否|String|指定icon距离锚点的方位。可选值：center：icon的中心放置在锚点最近的地方。left：icon的左侧放置在锚点最近的地方，即icon在锚点的右侧。right：icon的右侧放置在锚点最近的地方，即icon在锚点的左侧。top：icon的顶部放置在锚点最近的地方，即icon在锚点的底部。bottom：icon的底部放置在锚点最近的地方，即icon在锚点的顶部。top-left：icon的左上角放置在锚点最近的地方，即icon在锚点的右下角。top-right：icon的右上角放置在锚点最近的地方，即icon在锚点的左下角。bottom-left：icon的左下角放置在锚点最近的地方，即icon在锚点的右上角。bottom-right：icon的右下角放置在锚点最近的地方，即icon在锚点的左上角。默认为bottom。|
|opacity|否|Number|不透明度，取值范围：[0, 1]。0：完全透明。1：完全不透明。默认值为1。|
|rotation|否|Number|旋转角度，单位：弧度。取值说明：正数：顺时针旋转。如Math.PI/2，顺时针旋转90度。负数：逆时针旋转。如-Math.PI/2，逆时针旋转90度。以正北为0度，默认0，不旋转。|
|scale|否|Number|图片缩放，用来控制图片缩放。取值范围：大于0，默认1。|
|url|否|String|图片地址，可以是图片的URL路径或文件路径。默认有内置图片。说明请确保url允许跨域访问。|

### CustomPoiLabelOption

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|color|否|String|文字颜色。|
|fontSize|否|String|文字大小，默认14px。|
|offsetX|否|Number|横轴方向偏移值，单位：em。默认0，0代表与坐标点重合。|
|offsetY|否|Number|纵轴方向偏移值，单位：em。默认0，0代表与坐标点重合。|
|strokeColor|否|String|文字描边颜色，默认#FFF。|
|strokeWeight|否|Number|文字描边宽度，单位：px。取值范围：[0, 10]，默认0。|
|text|是|String|文字内容。|

## HWInfoWindow

### 概述

在地图上添加信息窗。

### 构造函数

|函数|描述|
|--|--|
|HWMapJsSDK.HWInfoWindow(infoWindowOptions)|infoWindowOptions：设置HWInfoWindow的属性，详情见InfoWindowOptions。|

### 方法

|方法|描述|参数类型|返回值|
|--|--|--|--|
|close()|关闭信息窗。|-|-|
|getContent()|获取信息窗内容。|-|String | Element|
|getPosition()|获取信息窗位置。|-|LatLng|
|open(marker)|打开弹出框。|HWMarker|-|
|setContent(content)|设置信息窗内容。|String | Element|-|
|setPosition(position)|设置信息窗位置。|LatLng|-|
|addListener(event, callback)|添加事件侦听。|event：String，见事件。callback：事件触发的回调函数。|-|
|removeListener(event, callback)|删除事件侦听。|event：String，见事件。callback：事件触发的回调函数。|-|

### 事件

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|事件|描述|用法|
|close|点击关闭。|infoWindow.addListener('close', callback)，参数callback为自定义回调函数。|

### InfoWindowOptions

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|content|否|String | Element|弹出框中的显示内容，可以是纯文本或者是HTML元素。窗口会根据内容自动调整大小。调用该接口需要注意跨站脚本攻击，SDK没有过滤有可能导致跨站脚本攻击的html代码，因为SDK无法区分是否为您主动使用。|
|map|是|HWMap|地图实例。|
|offset|否|[Number, Number]|设置x，y偏移量。默认位置是position指定的位置。|
|position|是|LatLng|弹出框显示的位置。|

## HWMarker

### 概述

用于添加标记，加载在该图层的元素，以设定好的规则显示在地图上。

### 构造函数

|函数|描述|
|--|--|
|HWMapJsSDK.HWMarker(markerOptions)|markerOptions：设置标记的属性，详情见MarkerOptions。|

### 方法

|方法|描述|参数类型|返回值|
|--|--|--|--|
|getAnimation()|获取标记动画。|-|String|
|getDraggable()|获取是否允许拖拽。|-|Boolean|
|getIcon()|获取Icon。|-|Object|
|getLabel()|获取Label。|-|String|
|getMap()|获取绑定的地图实例。|-|HWMap|
|getPosition()|获取HWMarker的位置。|-|LatLng|
|getProperties()|获取自定义属性。|-|Object|
|getZIndex()|获取HWMarker的z-index属性。|-|Number|
|setAnimation(animation)|设置标记动画。|StringDROP：首次放置标记时从上往下坠落动画。BOUNCE：标记弹跳动画。null：取消动画。|-|
|setDraggable(draggable)|设置是否允许拖拽。|Booleantrue：允许false：不允许|-|
|setIcon(icon)|设置Icon。|Object|-|
|setLabel(label)|设置Label。|String|-|
|setMap(map)|绑定到地图。|HWMap|-|
|setPosition(position)|设置HWMarker的位置。|LatLng|-|
|setZIndex(zIndex)|设置HWMarker的z-index属性，即z轴方向的叠加关系。|Number|-|
|addListener(event, callback)|添加事件侦听。|event：String，见事件。callback：事件触发的回调函数。|-|
|removeListener(event, callback)|删除事件侦听。|event：String，见事件。callback：事件触发的回调函数。|-|

### 事件

事件|描述|用法
|--|--|--|
|click|鼠标点击。|marker.addListener('click', callback)，参数callback为自定义回调函数。|
|contextmenu|鼠标右击。|marker.addListener('contextmenu', callback)，参数callback为自定义回调函数。|
|dbclick|鼠标双击。|marker.addListener('dbclick', callback)，参数callback为自定义回调函数。|
|icon_changed|icon改变。|marker.addListener('icon_changed', callback)，参数callback为自定义回调函数。|
|mousedown|鼠标按下。|marker.addListener('mousedown', callback)，参数callback为自定义回调函数。|
|mouseOut|鼠标移出。|marker.addListener('mouseOut', callback)，参数callback为自定义回调函数。|
|mouseOver|鼠标移入。|marker.addListener('mouseOver', callback)，参数callback为自定义回调函数。|
|mouseup|鼠标放开。|marker.addListener('mouseup', callback)，参数callback为自定义回调函数。|
|position_changed|position改变。|marker.addListener('position_changed', callback)，参数callback为自定义回调函数。|

### MarkerOptions

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|animation|否|String|设置标记动画。取值包括：DROP：首次放置标记时从上往下坠落动画。BOUNCE：标记弹跳动画。null：取消动画。|
|draggable|否|Boolean|是否允许拖拽。true：允许false：不允许默认值为false。|
|icon|否|String | MarkerIconOption|标记的图标，可以是图片的url路径或文件路径。|
|label|否|String | MarkerLabelOption|标记的标签，文字或者详细信息。|
|map|是|HWMap|地图实例。|
|position|是|LatLng|标记的位置。|
|properties|否|Object|自定义属性。|
|zIndex|否|Number|HWMarker的z-index属性，即z轴方向的叠加关系（相对于HWMarker、HWCircle、HWPolygon、HWPolyline、HWBezierCurve）。|

### MarkerIconOption

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|anchor|否|Array<Number>|图片锚点，有效值为两个元素的数组[x, y]，默认为[0.5, 1]，表示锚点在图片的下边中心。|
|anchorUnit|否|String|锚点值的单位，取值包括：fraction：图标的偏移量为锚点值与图片大小的乘积。pixels：图标的偏移量为锚点值，单位：像素。默认值为fraction。|
|opacity|否|Number|不透明度，取值范围：[0, 1]。0：完全透明。1：完全不透明。默认值为1。|
|quantityUrls|否|Array<QuantityUrls>|根据标记聚合数量定制图标数组。|
|rotation|否|Number|旋转角度，单位：弧度。取值说明：正数：顺时针旋转。如Math.PI/2，顺时针旋转90度。负数：逆时针旋转。如-Math.PI/2，逆时针旋转90度。以正北为0度，默认0，不旋转。|
|scale|否|Number|图片缩放，用来控制图片缩放。取值范围：大于0，默认1。|
|url|否|String|图片地址，可以是图片的URL路径或文件路径。默认有内置图片。说明gif格式建议使用HWOverlay。|

### MarkerLabelOption

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|color|否|String|文字颜色，默认#333。支持以下几种颜色设置：颜色名称：green十六进制：#ff0000RGB：rgb(0,0,0)RGBA：rgba(0,0,0,0.5)HSL：hsl(120,65%,75%)|
|fontSize|否|String|文字大小，默认14像素。|
|fontFamily|否|String|字体，默认sans-serif。|
|offsetX|否|Number|横轴方向偏移值，单位：像素。默认0，0代表与坐标点重合。|
|offsetY|否|Number|纵轴方向偏移值，单位：像素。默认0，0代表与坐标点重合。|
|strokeColor|否|String|文字描边，默认#FFF。|
|strokeWeight|否|Number|描边大小，单位：px。取值范围：[0, 10]，默认0。|
|text|是|String|文字内容。|

## HWMarkerCluster

### 概述

用于聚合地图上指定的Marker集合，能设定聚合距离以及聚合后显示的图片。

### 构造函数

|函数|描述|
|--|--|
|HWMapJsSDK.HWMarkerCluster(map, markers, options)|map：添加的地图图层。markers：要被聚合的HWMarker集合。options：HWMarkerCluster的属性，详情见MarkerClusterOptions。|

### 方法

|方法|描述|参数类型|返回值|
|--|--|--|--|
|addMarker(marker)|添加HWMarker。|HWMarker|-|
|addMarkers(markers)|添加HWMarker集合。|Array<HWMarker>|-|
|clearMarkers()|清除全部HWMarker。|-|-|
|getMarkers()|获取所有的HWMarker。|-|Array<HWMarker>|
|getZIndex()|获取HWMarkerCluster的z-index属性。||Number|
|removeMarker(marker)|删除HWMarker。|HWMarker|-|
|removeMarkers(markers)|删除HWMarker集合。|Array<HWMarker>|-|
|addListener(event, callback)|添加事件侦听。|event：String，见事件。callback：事件触发的回调函数。|-|
|removeListener(event, callback)|删除事件侦听。|event：String，见事件。callback：事件触发的回调函数。|-|
|setZIndex(zIndex)|设置z轴方向的叠加关系。|zIndex：Number。|

### 事件

|事件|描述|用法|
|--|--|--|
|click|鼠标点击。|markerCluster.addListener('click', callback)，参数callback为自定义回调函数。|
|mouseOut|鼠标移出。|markerCluster.addListener('mouseOut', callback)，参数callback为自定义回调函数。|
|mouseOver|鼠标移入。|markerCluster.addListener('mouseOver', callback)，参数callback为自定义回调函数。|

### MarkerClusterOptions

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|options.icon|否|String | MarkerClusterIconOption|HWMarker聚合后显示的图标，同HWMarker的设置。|
|options.label|否|String | MarkerClusterLabelOption|HWMarker聚合后的文字标签，同HWMarker的设置，默认值为聚合后的总数。|
|renderClusterMarker|否|Function|自定义聚合样式。renderClusterMarker(markers) => { return {icon,label}}markers：聚合后的标记数组。icon：MarkerClusterIconOptionlabel：MarkerClusterLabelOption|
|zIndex|否|Number|z轴方向的叠加关系（相对于HWMarker、HWCircle、HWPolygon和HWPolyline）。|

### MarkerClusterIconOption

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|anchor|否|Array<Number>|图片锚点，有效值为两个元素的数组[x, y]，默认为[0.5, 1]，表示锚点在图片的下边中心。|
|anchorUnit|否|String|锚点值的单位，取值包括：fraction：图标的偏移量为锚点值与图片大小的乘积。pixels：图标的偏移量为锚点值，单位：像素。默认值为fraction。|
|opacity|否|Number|不透明度，取值范围：[0, 1]。0：完全透明。1：完全不透明。默认值为1。|
|quantityUrls|否|Array<QuantityUrls>|根据标记聚合数量定制图标数组。|
|rotation|否|Number|旋转角度，单位：弧度。取值说明：正数：顺时针旋转。如Math.PI/2，顺时针旋转90度。负数：逆时针旋转。如-Math.PI/2，逆时针旋转90度。以正北为0度，默认0，不旋转。|
|scale|否|Number|图片缩放，用来控制图片缩放。取值范围：大于0，默认1。|
|url|否|String|图片地址，可以是图片的URL路径或文件路径。默认有内置图片。|

### MarkerClusterLabelOption

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|color|否|String|文字颜色，默认#333。支持以下几种颜色设置：颜色名称：green十六进制：#ff0000RGB：rgb(0,0,0)RGBA：rgba(0,0,0,0.5)HSL：hsl(120,65%,75%)|
|fontSize|否|String|文字大小，默认14像素。|
|fontFamily|否|String|字体，默认sans-serif。|
|offsetX|否|Number|横轴方向偏移值，单位：像素。默认0，0代表与坐标点重合。|
|offsetY|否|Number|纵轴方向偏移值，单位：像素。默认0，0代表与坐标点重合。|
|strokeColor|否|String|文字描边，默认#FFF。|
|strokeWeight|否|Number|描边大小，单位：px。取值范围：[0, 10]，默认0。|
|text|是|String|文字内容。|
|backgroundColor|否|String|文字背景色。支持以下几种颜色设置：颜色名称：green十六进制：#ff0000RGB：rgb(0,0,0)RGBA：rgba(0,0,0,0.5)HSL：hsl(120,65%,75%)|

## HWAutocomplete

### 概述

自动补全控件对象。

### 构造函数

|函数|描述|
|--|--|
|HWMapJsSDK.HWAutocomplete(searchBoxInput, acOptions)|searchBoxInput：HTML元素input。acOptions：控件的可选参数，详情见AutocompleteOptions。|

### 方法

|方法|描述|参数类型|返回值|
|--|--|--|--|
|addListener(eventName, callback)|自动补全列表条目点击事件的侦听函数。|eventName：事件名称，String，当前可选参数：site_changed。callback：Function，点击自动补全列表的回调函数。|-|
|getSite()|获取自动补全选中的Site对象。|-|Site|

### AutocompleteOptions

自动补全控件的可选参数对象，相关参数类型详细说明请参见参数说明章节描述。

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|bounds|否|LatLngBounds|搜索建议偏向的搜索范围。注意如果bounds、location都传的话，那么bounds优先。|
|countries|否|Array<String(=2)>|多个国家码，采用ISO 3166-1 alpha-2规范的2位国家码。|
|countryCode|否|String(=2)|在指定的国家内搜索，采用ISO 3166-1 alpha-2规范的2位国家码。|
|customHandler|否|Function|自定义自动补全搜索框，必选参数是name。|
|data|否|json|自定义自动补全的数据源，其中name字段是必选。|
|language|否|String(<=6)|搜索建议的语种，语种取值请参见支持的语言列表。如果不指定语种，返回地点的当地语言。|
|location|否|LatLng|搜索建议偏向的经纬度。|
|maxHeight|否|String|自动补全结果的显示高度，单位：px，默认值为200px。|
|poiType|否|String|返回指定POI类型的地点。取值包括：GEOCODE ： 仅返回地理编码结果（如国家、城市、街道等），而不返回商业结果（如酒店、餐馆、火车站等）。可以使用此类型过滤掉商业数据，消除用户输入中存在的歧义。例如：用户输入中国时，返回中国，不返回中国银行等数据。ADDRESS：GEOCODE的子集。仅返回具有精确地址的地理编码结果，不返回商业结果和粗粒度的地理编码结果（如国家、城市等），推荐在搜索完整地址时使用此类型。ESTABLISHMENT：GEOCODE的补集。仅返回作为商业结果（如酒店、餐馆、火车站等），不返回地理编码结果（如国家、城市、街道等）。例如：用户输入中国时，返回中国银行等数据，不返回中国。REGIONS ：GEOCODE的子集，仅返回与以下类型匹配的地点：LOCALITYSUBLOCALITYPOSTAL_CODECOUNTRYADMINISTRATIVE_AREA_LEVEL_1ADMINISTRATIVE_AREA_LEVEL_2CITIES：仅返回LOCALITY或ADMINISTRATIVE_AREA_LEVEL_3类型的地点。|
|politicalView|否|String(=2)|政治观点参数，采用ISO 3166-1 alpha-2规范的2位国家码。注意该参数已废弃。|
|radius|否|Number|Location的搜索半径，单位：米。取值范围：[1, 50000]，默认50000米。|

## HWSiteService

### 概述

该类提供位置搜索和地理编码接口。

### 构造函数

|函数|描述|
|--|--|
|HWMapJsSDK.HWSiteService()|-|

### 方法

|方法|描述|参数类型|返回值|
|--|--|--|--|
|geocode(request, callback)|正地理编码。|request：GeocodeRequest，正地理编码的请求体。callback：Function(GeocodeResult, StatusCode)，正地理编码的回调函数。|-|
|nearbySearch(request, callback)|周边搜索。|request：NearbySearchRequest，周边搜索的请求体。callback： Function(NearbySearchResult, StatusCode)，周边搜索的回调函数。|-|
|querySuggestion(request, callback)|地点搜索建议。|request：QuerySuggestionRequest，地点搜索建议的请求体。callback：Function(QuerySuggestionResult, StatusCode)，地点搜索建议的回调函数。|-|
|reverseGeocode(request, callback)|逆地理编码。|request： ReverseGeocodeRequest，逆地理编码的请求体。callback： Function( ReverseGeocodeResult, StatusCode)，逆地理编码的回调函数。|-|
|searchById(request, callback)|地点详情。|request：SearchByIdRequest，地点详情的请求体。callback： Function(SearchByIdResult, StatusCode)，地点详情的回调函数。|-|
|searchByText(request, callback)|关键字搜索。|request： SearchByTextRequest，关键字搜索的请求体。callback： Function(SearchByTextResult, StatusCode)，关键字搜索的回调函数。|-|

#### 说明

StatusCode是接口调用状态的返回码，具体请参见错误码。

### GeocodeRequest

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|address|是|String(<=512)|地点信息。|
|bounds|否|LatLngBounds|查询结果偏向的搜索范围。|
|language|否|String(<=6)|搜索结果的语种，语种取值请参见支持的语言列表。如果不指定语种，返回地点的当地语言。|
|politicalView|否|String(=2)|政治观点参数，采用ISO 3166-1 alpha-2规范的2位国家码。注意该参数已废弃。|

### GeocodeResult

|参数|参数类型|描述|
|sites|Array<Site>|如果查询成功，返回搜索结果。如果没有结果，返回空数组。说明Site对象不返回POI信息。|

### NearbySearchRequest

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|language|否|String(<=6)|搜索建议的语种，语种取值请参见支持的语言列表。如果不指定语种，返回地点的当地语言。|
|location|是|LatLng|当前用户的位置。|
|pageIndex|否|Number|当前页数。取值范围：[1, 60]，默认1。约束：pageindex*pagesize <= 60。|
|pageSize|否|Number|每页返回的记录数。取值范围：[1, 20]，默认20。|
|poiType|否|String|返回指定POI类型的地点。|
|politicalView|否|String(=2)|政治观点参数，采用ISO 3166-1 alpha-2规范的2位国家码。注意该参数已废弃。|
|query|否|String(<=512)|可以输入搜索关键字。|
|radius|否|Number|可以指定搜索半径，单位：米。取值范围：[1, 50000]，默认1000米。|

### NearbySearchResult

|参数|参数类型|描述|
|--|--|--|
|sites|Array<Site>|如果查询成功，返回搜索结果。如果没有结果，返回空数组。|
|totalCount|Number|如果查询成功，返回记录总数。如果没有结果，返回0。|

### QuerySuggestionRequest

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|bounds|否|LatLngBounds|搜索建议偏向的搜索范围。说明如果bounds、location都传的话，那么bounds优先。|
|countries|否|Array<String(=2)>|多个国家码，采用ISO 3166-1 alpha-2规范的2位国家码。|
|countryCode|否|String(=2)|在指定的国家内搜索，采用ISO 3166-1 alpha-2规范的2位国家码。|
|language|否|String(<=6)|搜索建议的语种，语种取值请参见支持的语言列表。如果不指定语种，返回地点的当地语言。|
|location|否|LatLng|搜索建议偏向的经纬度。|
|poiType|否|String|返回指定POI类型的地点。取值包括：GEOCODE ： 仅返回地理编码结果（如国家、城市、街道等），而不返回商业结果（如酒店、餐馆、火车站等）。可以使用此类型过滤掉商业数据，消除用户输入中存在的歧义。例如：用户输入中国时，返回中国，不返回中国银行等数据。ADDRESS：GEOCODE的子集。仅返回具有精确地址的地理编码结果，不返回商业结果和粗粒度的地理编码结果（如国家、城市等），推荐在搜索完整地址时使用此类型。ESTABLISHMENT：GEOCODE的补集。仅返回作为商业结果（如酒店、餐馆、火车站等），不返回地理编码结果（如国家、城市、街道等）。例如：用户输入中国时，返回中国银行等数据，不返回中国。REGIONS ：GEOCODE的子集，仅返回与以下类型匹配的地点：LOCALITYSUBLOCALITYPOSTAL_CODECOUNTRYADMINISTRATIVE_AREA_LEVEL_1ADMINISTRATIVE_AREA_LEVEL_2CITIES：仅返回LOCALITY或ADMINISTRATIVE_AREA_LEVEL_3类型的地点。|
|politicalView|否|String(=2)|政治观点参数，采用ISO 3166-1 alpha-2规范的2位国家码。注意该参数已废弃。|
|query|是|String(<=512)|搜索关键字。|
|radius|否|Number|Location的搜索半径，单位：米。取值范围：[1, 50000]，默认50000米。|

### QuerySuggestionResult

|参数|参数类型|描述|
|--|--|--|
|sites|Array<Site>|如果查询成功，返回搜索结果。如果没有结果，返回空数组。说明Site对象不返回POI信息。|

### ReverseGeocodeRequest

|参数|是否必选|参数类型|参数说明|
|--|--|--|--|
|language|否|String(<=6)|搜索结果的语种，语种取值请参见支持的语言列表。如果不指定语种，返回地点的当地语言。|
|location|是|LatLng|经纬度。|
|politicalView|否|String(=2)|政治观点参数，采用ISO 3166-1 alpha-2规范的2位国家码。注意该参数已废弃。|
|returnPoi|否|Boolean|是否返回POI的地点名称，默认true。说明目前逆地理接口，只能返回机场的名称，其他POI不支持返回名称。|

### ReverseGeocodeResult

|参数|参数类型|参数说明|
|--|--|--|
|sites|Array<Site>|如果查询成功，返回搜索结果。如果没有结果，返回空数组。说明Site对象不返回POI信息。|

### SearchByIdRequest

|参数|是否必选|参数类型|参数说明|
|--|--|--|--|
|language|否|String(<=6)|搜索结果的语种，语种取值请参见支持的语言列表。如果不指定语种，返回地点的当地语言。|
|politicalView|否|String(=2)|政治观点参数，采用ISO 3166-1 alpha-2规范的2位国家码。注意该参数已废弃。|
|siteId|是|String(<=256)|地点ID。|

### SearchByIdResult

|参数|参数类型|参数说明|
|--|--|--|
|site|Site|如果查询成功，返回地点详情；如果错误，返会错误描述。|

### SearchByTextRequest

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|countries|否|Array<String(=2)>|多个国家码，采用ISO 3166-1 alpha-2规范的2位国家码。|
|countryCode|否|String(=2)|在指定的国家内搜索，采用ISO 3166-1 alpha-2规范的2位国家码。|
|language|否|String(<=6)|搜索结果的语种，语种取值请参见支持的语言列表。如果不指定语种，返回地点的当地语言。|
|location|否|LatLng|搜索结果偏向的经纬度。|
|pageSize|否|Number|每页返回的记录数。取值范围：[1, 20]，默认20。|
|pageIndex|否|Number|当前页数。取值范围：[1, 60]，默认1。约束：pageindex*pagesize <= 60。|
|poiType|否|String|返回指定POI类型的地点。|
|politicalView|否|String(=2)|政治观点参数，采用ISO 3166-1 alpha-2规范的2位国家码。注意该参数已废弃。|
|query|是|String(<=512)|搜索关键字。|
|radius|否|Number|Location的搜索半径，单位：米。取值范围：[1, 50000]，默认50000米。|

### SearchByTextResult

|参数|参数类型|描述|
|--|--|--|
|sites|Array<Site>|如果查询成功，返回搜索结果。如果没有结果，返回空数组。|
|totalCount|int|如果查询成功，返回记录总数。如果没有结果，返回0。|

## HWDirectionsService

### 概述

该类提供路径规划服务，支持步行路径规划、骑行路径规划和驾车路径规划。

### 构造函数

|函数|描述|
|--|--|
|HWMapJsSDK.HWDirectionsService()|-|

### 方法

|方法|描述|参数类型|返回值|
|--|--|--|--|
|routeWalking(request, callback)|步行路径规划。|request: DirectionsRequest，路线规划的请求体。callback： Function(DirectionsResult, StatusCode)，路径规划的回调函数。|-|
|routeBicycling(request, callback)|骑行路径规划。|request：DirectionsRequest，路线规划的请求体。callback： Function(DirectionsResult, StatusCode)，路径规划的回调函数。|-|
|routeDriving(request, callback)|驾车路径规划。|request：DrivingDirectionsRequest，驾车路径规划的请求体。callback： Function(DirectionsResult, StatusCode)，驾车路径规划的回调函数。|-|

#### 说明

StatusCode是接口调用状态的返回码，具体请参见错误码。

### DirectionsRequest

|名称|是否必选|类型|描述|
|--|--|--|--|
|origin|是|LatLng|起点的经纬度。|
|destination|是|LatLng|终点的经纬度。|

### DirectionsResult

|名称|类型|描述|
|--|--|--|
|routes|Array<Route>|从起点到目的地的规划路径。|

### DrivingDirectionsRequest

|名称|是否必选|类型|描述|
|--|--|--|--|
|origin|是|LatLng|起点的经纬度。|
|destination|是|LatLng|终点的经纬度。|
|waypoints|否|Array<LatLng>|途经点。最多可以输入5个途经点。|
|viaType|否|Boolean|途径点类型，是via类型还是stopover类型。false：stopover类型true：via类型默认值为false。|
|optimize|否|Boolean|是否对途径点进行优化。false：不进行途径点优化。true：进行途径点优化。默认值为false。|
|alternatives|否|Boolean|如果设置为true，可以返回多条规划路线结果。取值包括：truefalse默认值为false。说明如果设置了途经点时，不能使用多路线功能。|
|avoid|否|Array<Number>|表示计算出的路径应避免所指示的特性，取值包括：1：避免经过收费的公路。2：避开高速公路。默认按时间最短返回。|
|departAt|否|Number|预计出发时间。以自UTC 1970年1月1日午夜以来的秒数为单位。必须是当前或者未来时间，不能是过去时间。|
|trafficMode|否|Number|时间预估模型。取值包括：0：best guess1：路况差于历史平均水平2：路况优于历史平均水平默认值为0。|

## HWDirectionsRenderer

### 概述

根据路径规划接口的返回结果，在地图使用polyline呈现出规划的路径。

### 构造函数

|函数|描述|
|--|--|
|HWMapJsSDK.HWDirectionsRenderer()|-|

### 方法

|方法|描述|参数类型|返回值|
|--|--|--|--|
|setMap(map)|绑定到地图。|HWMap|-|
|getMap()|获取绑定的地图实例。|-|HWMap|
|setOptions(options)|设置路径的样式。|RendererOptions|-|
|getOptions()|获取路径的样式。|-|RendererOptions|
|setDirections(directionsResult, options)|根据路径规划接口返回数据显示路径，其中options为可选参数。|directionsResult：DirectionsResult，路径规划结果。options：RendererOptions，路线的渲染样式。|Array<HWPolyline>|

### RendererOptions

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|strokeColor|否|String|用于设置颜色和透明度，表示路径颜色，默认为黑色。支持以下几种颜色设置：颜色名称：green十六进制：#ff0000RGB：rgb(0,0,0)RGBA：rgba(0,0,0,0.5)HSL：hsl(120,65%,75%)|
|strokeWeight|否|Number|路径宽度，单位：像素，默认为1像素。|
|visible|否|Boolean|表示可见性。true：可见false：不可见默认值为true。|
|zIndex|否|Number|同HWPolyline的zIndex。|

## 参数说明

### Address

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|countryCode|否|String|国家码。|
|country|否|String|国家名。|
|state|否|String|省/州。|
|county|否|String|县/市。|
|town|否|String|镇/区。|
|settlement|否|String|定居点。|

### AddressDetail

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|countryCode|否|String|国家码，采用ISO 3166-1 alpha-2。|
|country|否|String|国家名。|
|adminArea|否|String|国家下面的一级行政区，一般是省/州。|
|subAdminArea|否|String|国家下面的二级行政区，一般是市。|
|locality|否|String|城市。|
|subLocality|否|String|国家下面的四级行政区，一般是镇。|
|streetNumber|否|String|街道号。|
|thoroughfare|否|String|街道名。|
|postalCode|否|String|邮政编码。|

### AutocompletePrediction

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|description|是|String|预测的描述。|
|matchedKeywords|是|Array<Word>|输入的关键字在description里的匹配位置。|
|matchedWords|是|Array<Word>|description包含的单词，以及位置。|

### Coordinate

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|lat|是|Number|纬度，取值范围：[-90, 90]。|
|lng|是|Number|经度，取值范围：[-180, 180]。|

### CoordinateBounds

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|northeast|是|Coordinate|东北角的位置。|
|southwest|是|Coordinate|西南角的位置。|

### FitViewOptions

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|padding|否|Array<Number>|四周边距，顺序是上下左右。|
|maxZoom|否|Number|地图最大缩放级别，取值范围：[2, 20]。|

### GroundOverlayLatLngBounds

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|east|是|Number|东北角经度，取值范围：[-180, 180]。|
|west|是|Number|西南角经度，取值范围：[-180, 180]。|
|north|是|Number|东北角纬度，取值范围：[-90, 90]。|
|south|是|Number|西南角纬度，取值范围：[-90, 90]。|

### LatLng

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|lat|是|Number|纬度，取值范围：[-90, 90]。|
|lng|是|Number|经度，取值范围：[-180, 180]。|

### LatLngBounds

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|ne|是|Coordinate|东北角的位置。|
|sw|是|Coordinate|西南角的位置。|

### NaviLineCodes

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|codes|否|Array<Object>|每段路的路牌内容和雪碧图。如果没有设置，不显示路牌。例如：[{code: 'code2',codeSprite: '110033'}, …...]，为indexes的长度1/2。|
|indexes|否|Array|每段路的点的下标索引。如果没有设置，不显示路牌。例如：[0, 50,120,180]Indexes的长度为偶数，每两个一对。|

### NaviLineFillColors

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|colors|否|Array|每段路的颜色。如果没有设置，使用默认颜色显示。例如：['#ff0000', 'rgb(255, 0, 0)', ……]长度为indexes的长度的1/2。|
|indexes|否|Array|每段路的点的下标索引。例如：[0, 50, 120, 180, …...]Indexes的长度为偶数，每两个一对。|

### NaviLineTexts

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|texts|否|Array<Object>|每段路的路名。如果没有设置，不显示路名。例如：[{name: '长安街', name_da: 'changanjie'}, …...]，第一个name是本地语的路名，第二个name_xx是设置语言的路名，属性名规则是name_{设置语言码}。数组长度是indexes1/2。|
|indexes|否|Array|每段路的点的下标索引。如果没有设置，不显示路名。例如：[0, 50, 120, 180, …...]Indexes的长度为偶数，每两个一对。|

### OpeningHours

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|texts|是|Array<String>|每个星期的开放时间段的描述。|
|periods|是|Array<Period>|开放时间段的详细说明。|

### Path

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|steps|是|Array<Step>|路线分段信息。|
|distance|否|Number|行驶距离，单位：米。|
|duration|否|Number|行驶时长，单位：秒。|
|distanceText|否|String|Distance的文本描述。|
|durationInTraffic|否|Number|基于实时路况计算出来的行驶时长，单位：秒。|
|durationText|否|String|Duration的文本描述。|
|startLocation|是|Coordinate|出发地的经纬度。|
|startAddress|否|String|startLocation对应的地址详情。|
|endLocation|是|Coordinate|目的地的经纬度。|
|endAddress|否|String|endLocation对应的地址详情。|

### Period

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|open|是|TimeOfWeek|开放时间。|
|close|否|TimeOfWeek|关闭时间。|

### Poi

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|poiTypes|是|Array<String>|POI类型。|
|hwPoiTypes|是|Array<String>|华为POI分类体系。说明推荐使用hwPoiTypes。|
|phone|否|String|电话号码。|
|internationalPhone|否|String|国际电话号码。|
|rating|否|Number|评分。|
|websiteUrl|否|String|网址。|
|openingHours|否|OpeningHours|营业时间。|
|photoUrls|否|Array<String>|图片地址。|
|priceLevel|否|Number|价格等级，取值范围：[0, 4]。说明仅地址详情接口返回。|
|businessStatus|否|String|营业状态，其中包括：OPEN_NOW：正在营业。CLOSE_NOW：已休息。CLOSED_TEMPORARILY：临时关闭。CLOSED_PERMANENTLY：永久关闭。说明仅地址详情接口返回。|
|icon|否|String|POI类型图标。|

### QuantityUrls

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|num|是|Number|标记聚合数量。|
|url|是|String|图标地址，可以是图片的URL路径或文件路径。默认有内置图片。|

### Route

|参数|是否必选|参数类型|描述|
|--|--|--|--||取值
|paths|是|Array<Path>|路线规划信息。|-|
|optimizedWaypoints|否|Array<Number>|当viaType=false且optimize=true时才会有结果，表示进行路径优化之后途径点的索引。|-|
|bounds|否|CoordinateBounds|路线边界范围。|-|
|hasRestrictedRoad|否|Number|此路段是否包含私家/限制用途。|取值包括：0：没有1：有|
|dstInRestrictedArea|否|Number|终点是否在限制区域，进入前请确认。|
|crossCountry|否|Number|是否穿越国境线（出发前请确认COVID-19边境限制）。|
|crossMultiCountries|否|Number|是否穿越（多条）国境线。|
|hasRoughRoad|否|Number|此路段是否经过崎岖道路。|
|dstInDiffTimeZone|否|Number|目的地是否在不同时区。|
|hasFerry|否|Number|是否途径轮渡。|
|hasTrafficLight|否|Number|是否含红绿灯。|
|hasTolls|否|Number|此路段是否含收费站。|
|trafficLightNum|否|Number|红绿灯个数。|-|
|overviewPolyline|否|String|该路线的编码后的折线。|-|

### Site

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|siteId|是|String|地点的唯一主键。|
|name|否|String|地点名称。|
|formatAddress|是|String|格式化的地点详细地址。|
|address|是|AddressDetail|地址详细信息。|
|location|否|Coordinate|地点的经纬度。|
|viewport|否|CoordinateBounds|地点的视口范围。说明输入提示不支持返回此字段。|
|distance|否|Number|预测地点和传参location之间的直线距离，单位：米。说明目前仅关键字搜索、周边搜索和地点搜索建议接口支持返回此字段。|
|poi|否|Poi|如果地点是POI，返回POI信息。|
|utcOffset|否|Number|位置所在时区和UTC时区的差值，单位：分钟。说明仅地点详情接口返回。|
|prediction|否|AutocompletePrediction|地点搜索建议和自动补全接口，返回自动填充的描述信息。|

### Step

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|distance|否|Number|行驶距离，单位：米。|
|distanceText|否|String|Distance的文本描述。|
|durationInTraffic|否|Number|基于实时路况计算出来的行驶时长，单位：秒。|
|durationText|否|String|Duration的文本描述。|
|startLocation|否|Coordinate|出发地的经纬度。|
|startAddress|否|String|startLocation对应的地址详情。|
|endLocation|否|Coordinate|目的地的经纬度。|
|endAddress|否|String|endLocation对应的地址详情。|
|action|否|String|当前步骤要执行的操作，取值包括：TURN_SLIGHT_LEFTTURN_SHARP_LEFTUTURN_LEFTTURN_LEFTTURN_SLIGHT_RIGHTTURN_SHARP_RIGHTUTURN_RIGHTTURN_RIGHTSTRAIGHTRAMP_LEFTRAMP_RIGHTMERGEFORK_LEFTFORK_RIGHTFERRY（暂不支持）FERRY_TRAIN（暂不支持）ROUNDABOUT_LEFTROUNDABOUT_RIGHT|
|polyline|否|Array<Coordinate>|此路段的一系列坐标点（包含起点和终点坐标）。|
|roadName|否|String|路名。|
|orientation|否|Number|道路方向。取值包括：0：双向1：正向2：反向|
|instruction|否|String|文字指引。|

### TimeOfWeek

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|week|是|Number|0：星期日1：星期一2：星期二3：星期三4：星期四5：星期五6：星期六|
|time|是|String|24小时制时间，hhmm格式。|

### Word

|参数|是否必选|参数类型|描述|
|--|--|--|--|
|offset|是|Number|单词在description里的偏移位。|
|value|是|Number|单词。|
