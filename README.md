# 华为地图 Vue3 组件库

[![npm version](https://badge.fury.io/js/huawei-map-vue3.svg)](https://badge.fury.io/js/huawei-map-vue3)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

基于华为地图API开发的Vue3地图组件库，提供完整的地图功能封装，包括地图显示、标记管理、信息窗、搜索功能和路径规划等功能。

## 功能特性

### 🗺️ 基础功能

- **地图显示**: 支持华为地图的基础显示功能
- **标记管理**: 完整的标记添加、删除、编辑功能
- **信息窗**: 支持自定义内容的信息窗显示
- **配置管理**: 可视化的地图配置面板
- **事件监控**: 实时的地图事件监控和日志
- **样式定制**: 支持多种地图样式和主题
- **响应式设计**: 适配不同屏幕尺寸

### 🔍 搜索功能 (v1.1.0)

- **地点搜索**: 根据关键词搜索地点、POI
- **地理编码**: 地址转坐标 (Geocoding)
- **逆地理编码**: 坐标转地址 (Reverse Geocoding)
- **周边搜索**: 搜索指定位置周边的POI
- **搜索建议**: 实时搜索建议和自动补全
- **搜索历史**: 保存和管理搜索历史记录

### 🛣️ 路径规划功能 (v1.1.0)

- **驾车路径**: 支持多种驾车策略（最快、最短、避免拥堵）
- **步行路径**: 步行导航路径规划
- **骑行路径**: 自行车路径规划
- **多点路径**: 支持途经点的路径规划
- **路径展示**: 在地图上绘制路径线条和标记
- **路径统计**: 距离、时间等详细信息

## 项目结构

```
src/
├── components/           # Vue组件
│   ├── HuaweiMap.vue    # 主地图组件
│   ├── MapConfigPanel.vue      # 地图配置面板
│   ├── MapEventMonitor.vue     # 事件监控面板
│   ├── MarkerPanel.vue         # 标记管理面板
│   └── InfoWindowPanel.vue     # 信息窗管理面板
├── composables/         # 组合式函数
│   ├── useMapConfig.ts         # 地图配置管理
│   ├── useMapEvents.ts         # 地图事件管理
│   ├── useMapMarkers.ts        # 标记管理
│   └── useInfoWindows.ts       # 信息窗管理
├── config/              # 配置文件
│   └── map-config.ts           # 地图默认配置
├── utils/               # 工具函数
│   └── map-loader.ts           # 地图API加载器
├── types/               # 类型定义
│   └── huawei-map.d.ts         # 华为地图API类型声明
├── examples/            # 示例文件
│   ├── BasicExample.vue        # 基础示例
│   └── AdvancedExample.vue     # 高级示例
└── tests/               # 测试文件
    └── map-component.test.ts   # 组件测试
```

## 安装

```bash
npm install huawei-map-vue3
```

或者使用 yarn：

```bash
yarn add huawei-map-vue3
```

或者使用 pnpm：

```bash
pnpm add huawei-map-vue3
```

## 快速开始

### 1. 配置API密钥

在您的项目根目录创建 `.env` 文件，并配置华为地图API密钥：

```env
VITE_HUAWEI_MAP_API_KEY=your_huawei_map_api_key_here
VITE_HUAWEI_MAP_API_URL=https://mapapi.cloud.huawei.com/mapjs/v1/api/js
VITE_DEFAULT_MAP_CENTER_LAT=23.130415047434678
VITE_DEFAULT_MAP_CENTER_LNG=113.32380294799805
VITE_DEFAULT_MAP_ZOOM=8
```

> **注意**: 所有功能（包括搜索和路径规划）都会自动使用 `.env` 文件中配置的API密钥，无需手动传入。

### 2. 引入组件

在您的 Vue 应用中引入组件：

```javascript
// main.js 或 main.ts
import { createApp } from 'vue'
import App from './App.vue'

// 引入样式（可选）
import 'huawei-map-vue3/dist/style.css'

createApp(App).mount('#app')
```

## 基础用法

### 简单地图

```vue
<template>
  <HuaweiMap
    :center="{ lat: 39.9042, lng: 116.4074 }"
    :zoom="10"
    width="100%"
    height="400px"
    @map-ready="onMapReady"
  />
</template>

<script setup>
import { HuaweiMap } from 'huawei-map-vue3'

const onMapReady = (map) => {
  console.log('地图加载完成:', map)
}
</script>
```

### 添加标记

```vue
<template>
  <HuaweiMap
    ref="mapRef"
    :center="center"
    :zoom="zoom"
    @map-ready="onMapReady"
  />
</template>

<script setup>
import { ref } from 'vue'
import { HuaweiMap } from 'huawei-map-vue3'

const mapRef = ref()
const center = ref({ lat: 39.9042, lng: 116.4074 })
const zoom = ref(10)

const onMapReady = () => {
  // 添加标记
  mapRef.value.addMarker({
    position: { lat: 39.9042, lng: 116.4074 },
    title: '北京',
    content: '中国首都'
  })
}
</script>
```

### 使用 Composables

```vue
<template>
  <div>
    <HuaweiMap
      ref="mapRef"
      :center="config.center"
      :zoom="config.zoom"
      @map-ready="onMapReady"
    />
    <button @click="addRandomMarker">添加随机标记</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { HuaweiMap, useMapConfig, useMapMarkers } from 'huawei-map-vue3'

const mapRef = ref()
const { config } = useMapConfig({
  center: { lat: 39.9042, lng: 116.4074 },
  zoom: 10
})

const { addMarker } = useMapMarkers()

const onMapReady = (map) => {
  console.log('地图加载完成:', map)
}

const addRandomMarker = () => {
  const randomLat = 39.9042 + (Math.random() - 0.5) * 0.1
  const randomLng = 116.4074 + (Math.random() - 0.5) * 0.1

  addMarker({
    position: { lat: randomLat, lng: randomLng },
    title: '随机标记',
    content: `位置: ${randomLat.toFixed(4)}, ${randomLng.toFixed(4)}`
  })
}
</script>
```

### 搜索功能使用示例 (v1.1.0)

```vue
<template>
  <div>
    <!-- 搜索面板 -->
    <SearchPanel
      :map-center="mapCenter"
      @result-selected="onSearchResult"
      @geocode-result="onGeocodeResult"
    />

    <!-- 地图 -->
    <HuaweiMap
      :center="mapCenter"
      :zoom="10"
      width="100%"
      height="400px"
      @map-ready="onMapReady"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { HuaweiMap, SearchPanel, useSearch } from 'huawei-map-vue3'

const mapCenter = ref({ lat: 39.9042, lng: 116.4074 })

// 使用搜索功能
const { searchPlace, geocode, getSuggestions } = useSearch()

const onSearchResult = (result) => {
  console.log('搜索结果:', result)
  // 更新地图中心到搜索结果位置
  mapCenter.value = result.location
}

const onGeocodeResult = (result) => {
  console.log('地理编码结果:', result)
}

// 手动搜索示例
const searchExample = async () => {
  try {
    const results = await searchPlace('北京天安门', {
      center: mapCenter.value,
      radius: 5000
    })
    console.log('搜索结果:', results)
  } catch (error) {
    console.error('搜索失败:', error)
  }
}
</script>
```

### 路径规划功能使用示例 (v1.1.0)

```vue
<template>
  <div>
    <!-- 路径规划面板 -->
    <RoutingPanel
      :map="map"
      @route-planned="onRoutePlanned"
      @route-displayed="onRouteDisplayed"
    />

    <!-- 地图 -->
    <HuaweiMap
      :center="mapCenter"
      :zoom="10"
      width="100%"
      height="400px"
      @map-ready="onMapReady"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { HuaweiMap, RoutingPanel, useRouting } from 'huawei-map-vue3'

const mapCenter = ref({ lat: 39.9042, lng: 116.4074 })
const map = ref(null)

// 使用路径规划功能
const { planDrivingRoute, planWalkingRoute, displayRoute } = useRouting(map)

const onMapReady = (mapInstance) => {
  map.value = mapInstance
}

const onRoutePlanned = (route) => {
  console.log('路径规划结果:', route)
}

const onRouteDisplayed = (route) => {
  console.log('路径已显示:', route)
}

// 手动路径规划示例
const routeExample = async () => {
  try {
    const route = await planDrivingRoute(
      { lat: 39.9042, lng: 116.4074 }, // 起点
      { lat: 39.9142, lng: 116.4174 }, // 终点
      { strategy: 'fastest' } // 选项
    )

    // 在地图上显示路径
    displayRoute(route)
    console.log('路径规划成功:', route)
  } catch (error) {
    console.error('路径规划失败:', error)
  }
}
</script>
```

## 组件API

### HuaweiMap 组件

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| center | `{lat: number, lng: number}` | `{lat: 23.13, lng: 113.32}` | 地图中心点 |
| zoom | `number` | `8` | 缩放级别 |
| width | `string` | `'100%'` | 地图宽度 |
| height | `string` | `'400px'` | 地图高度 |
| language | `string` | `'zh'` | 地图语言 |
| sourceType | `'vector' \| 'raster'` | `'raster'` | 瓦片类型 |
| mapType | `'ROADMAP' \| 'TERRAIN'` | `'ROADMAP'` | 地图类型 |
| zoomControl | `boolean` | `true` | 是否显示缩放控件 |
| scaleControl | `boolean` | `false` | 是否显示比例尺 |
| enableEventMonitor | `boolean` | `false` | 是否启用事件监控 |

#### Events

| 事件名 | 参数 | 描述 |
|--------|------|------|
| map-ready | `(map)` | 地图加载完成 |
| map-click | `(event)` | 地图点击事件 |
| map-dblclick | `(event)` | 地图双击事件 |
| center-changed | `(center)` | 中心点变化 |
| zoom-changed | `(zoom)` | 缩放级别变化 |
| marker-click | `(marker, event)` | 标记点击事件 |

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|--------|------|--------|------|
| addMarker | `(markerData)` | `string` | 添加标记 |
| removeMarker | `(id)` | `boolean` | 删除标记 |
| clearMarkers | `()` | `number` | 清空所有标记 |
| addInfoWindow | `(infoWindowData)` | `string` | 添加信息窗 |
| openInfoWindow | `(id)` | `boolean` | 打开信息窗 |
| closeInfoWindow | `(id)` | `boolean` | 关闭信息窗 |

## API 文档

### 组件

- `HuaweiMap` - 主地图组件
- `MapConfigPanel` - 地图配置面板
- `MapEventMonitor` - 事件监控面板
- `MarkerPanel` - 标记管理面板
- `InfoWindowPanel` - 信息窗管理面板

### Composables

- `useMapConfig` - 地图配置管理
- `useMapEvents` - 地图事件管理
- `useMapMarkers` - 标记管理
- `useInfoWindows` - 信息窗管理

### 工具函数

- `loadHuaweiMapAPI` - 华为地图API加载器
- `isHuaweiMapAPILoaded` - 检查API是否已加载

详细的API文档请参考组件的TypeScript类型定义。

## 开发

如果您想为此项目做贡献或在本地开发：

### 项目设置

```bash
# 克隆项目
git clone https://github.com/space-water-bear/hw-map.git
cd hw-map

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置您的华为地图API密钥

# 启动开发服务器
npm run dev

# 构建库文件
npm run build

# 运行测试
npm run test:unit

# 代码检查
npm run lint
```

### 技术栈

- **Vue 3**: 使用Composition API
- **TypeScript**: 完整的类型支持
- **Vite**: 快速的开发构建工具
- **华为地图API**: 地图服务提供商

## 注意事项

1. **API密钥**: 使用前需要在华为开发者平台申请地图API密钥
2. **域名限制**: 确保您的域名已在华为开发者平台配置
3. **网络环境**: 需要能够访问华为地图API服务
4. **浏览器兼容性**: 支持现代浏览器，建议使用Chrome、Firefox、Safari等

## 许可证

MIT License

## 贡献

我们欢迎所有形式的贡献！请阅读我们的贡献指南：

1. Fork 这个项目
2. 创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 Pull Request

### 报告问题

如果您发现了bug或有功能建议，请在 [GitHub Issues](https://github.com/space-water-bear/hw-map/issues) 中创建一个issue。

## 更新日志

### v1.0.0

- ✅ 基础地图显示功能
- ✅ 标记管理功能
- ✅ 信息窗功能
- ✅ 事件监控功能
- ✅ 配置管理功能
- ✅ 示例和文档

---

感谢使用华为地图Vue3组件！🎉
