#!/bin/bash

# 华为地图Vue3组件快速发布脚本
# 使用方法: ./scripts/publish-now.sh

set -e

echo "🚀 开始发布华为地图Vue3组件到NPM..."

# 检查是否已登录NPM
echo "📋 检查NPM登录状态..."
if ! npm whoami > /dev/null 2>&1; then
    echo "❌ 请先登录NPM: npm login"
    exit 1
fi

echo "✅ NPM登录状态正常"

# 检查包名是否可用
echo "📦 检查包名可用性..."
if npm view huawei-map-vue3 > /dev/null 2>&1; then
    echo "⚠️  包名 'huawei-map-vue3' 已存在，请检查版本号"
else
    echo "✅ 包名可用"
fi

# 构建项目
echo "🔨 构建项目..."
npm run build || {
    echo "❌ 构建失败"
    exit 1
}

# 检查构建产物
echo "📁 检查构建产物..."
if [ ! -f "dist/index.d.ts" ]; then
    echo "❌ 类型定义文件缺失"
    exit 1
fi

if [ ! -f "dist/huawei-map-vue3.js" ]; then
    echo "❌ ES模块文件缺失"
    exit 1
fi

if [ ! -f "dist/huawei-map-vue3.umd.cjs" ]; then
    echo "❌ UMD模块文件缺失"
    exit 1
fi

echo "✅ 构建产物检查通过"

# 显示即将发布的信息
echo "📋 发布信息:"
echo "  包名: $(node -p "require('./package.json').name")"
echo "  版本: $(node -p "require('./package.json').version")"
echo "  作者: $(node -p "require('./package.json').author.name")"

# 发布到NPM
echo "📤 发布到NPM..."
npm publish || {
    echo "❌ 发布失败"
    exit 1
}

echo "🎉 发布成功!"
echo "📦 包地址: https://www.npmjs.com/package/huawei-map-vue3"
echo "📚 安装命令: npm install huawei-map-vue3"

echo "🎊 发布流程完成!"
