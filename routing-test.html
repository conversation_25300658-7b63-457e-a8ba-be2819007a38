<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>华为地图路径规划测试 - v1.1.0</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
      min-height: 100vh;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px 0;
      text-align: center;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .header h1 {
      font-size: 28px;
      margin-bottom: 8px;
    }

    .header p {
      font-size: 16px;
      opacity: 0.9;
    }

    .nav-bar {
      background: white;
      padding: 15px 0;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    .nav-bar a {
      display: inline-block;
      margin: 0 15px;
      padding: 8px 16px;
      background: #007bff;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      transition: background 0.3s ease;
    }

    .nav-bar a:hover {
      background: #0056b3;
    }

    .nav-bar a.back {
      background: #6c757d;
    }

    .nav-bar a.back:hover {
      background: #545b62;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0;
    }

    .app-container {
      background: #f5f5f5;
      min-height: calc(100vh - 140px);
    }

    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 400px;
      color: #666;
      font-size: 16px;
    }

    .loading:before {
      content: "";
      width: 20px;
      height: 20px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #007bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 10px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .footer {
      text-align: center;
      padding: 20px;
      color: #666;
      font-size: 14px;
      background: white;
      border-top: 1px solid #e0e0e0;
    }

    @media (max-width: 768px) {
      .header h1 {
        font-size: 24px;
      }

      .nav-bar a {
        margin: 5px;
        font-size: 14px;
      }
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>华为地图路径规划测试</h1>
    <p>v1.1.0 - HWAutocomplete 集成测试</p>
  </div>

  <div class="nav-bar">
    <a href="/index-test.html" class="back">← 返回测试中心</a>
    <a href="/advanced-test.html">高级功能测试</a>
    <a href="/simple-test.html">简单测试</a>
    <a href="/autocomplete-test.html">自动补全测试</a>
  </div>

  <div class="container">
    <div class="app-container">
      <div id="routing-test-app">
        <div class="loading">
          正在加载路径规划测试页面...
        </div>
      </div>
    </div>
  </div>

  <div class="footer">
    <p>华为地图路径规划测试 - Vue3 组件库 v1.1.0</p>
    <p>请确保已在 .env 文件中配置华为地图 API 密钥 (VITE_HUAWEI_MAP_API_KEY)</p>
    <p>测试功能：起点/终点自动补全、途经点管理、多种路径类型、实时规划</p>
  </div>

  <script type="module" src="/src/test-pages/routing-main.ts"></script>
</body>
</html>
