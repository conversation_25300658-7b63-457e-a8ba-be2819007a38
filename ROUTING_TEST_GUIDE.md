# 路径规划测试页面使用指南

## 概述

新的路径规划测试页面 (`routing-test.html`) 专门用于测试华为地图的路径规划功能和 HWAutocomplete 集成。这个页面提供了一个专注的环境来测试和演示路径规划的各种功能。

## 页面访问

访问地址：`http://localhost:5173/routing-test.html`

## 页面布局

### 左侧面板
1. **路径规划面板** - 主要的路径规划控制界面
2. **快速测试面板** - 预设路线快速测试按钮
3. **功能特性说明** - 当前支持的功能列表

### 右侧地图
1. **华为地图显示区域** - 显示路径和标记
2. **地图控制按钮** - 地图操作快捷按钮
3. **地图状态显示** - 实时显示地图状态信息

### 底部日志
- **事件日志面板** - 记录所有路径规划相关的操作和结果

## 主要功能测试

### 1. 自动补全功能测试

#### 起点自动补全
1. 在"起点"输入框中输入地点名称（如"天安门"）
2. 观察自动补全建议列表
3. 选择一个建议项
4. 确认起点坐标已自动设置

#### 终点自动补全
1. 在"终点"输入框中输入地点名称（如"故宫"）
2. 选择合适的建议项
3. 确认终点坐标已自动设置

#### 途经点自动补全
1. 点击"+ 添加途经点"按钮
2. 在新的途经点输入框中输入地点名称
3. 选择建议项
4. 可以添加多个途经点（最多5个）

### 2. 快速测试功能

#### 预设路线测试
- **🏛️ 北京景点路线**：天安门 → 王府井 → 故宫
- **🏙️ 上海商务路线**：外滩 → 南京路 → 陆家嘴
- **🌸 广州美食路线**：广州塔 → 上下九 → 沙面岛

点击任一预设路线按钮，系统会：
1. 自动设置起点、终点和途经点
2. 在地图上添加相应标记
3. 调整地图视图到合适位置

### 3. 路径规划测试

#### 路径类型选择
- **🚗 驾车**：支持多种驾车策略（最快、最短、避开拥堵等）
- **🚶 步行**：步行路径规划
- **🚴 骑行**：自行车路径规划
- **🚌 公交**：公共交通路径规划

#### 执行路径规划
1. 确保起点和终点已设置
2. 选择合适的路径类型
3. 配置路径选项（如驾车策略）
4. 点击"开始规划"按钮
5. 查看规划结果和路径详情

### 4. 地图控制功能

#### 地图操作
- **🎯 重置视图**：恢复到默认视图
- **🗺️ 切换地图**：切换地图类型（功能待实现）
- **📍 定位北京**：快速定位到北京

#### 清除功能
- **🗑️ 清除所有**：清除所有路径和标记
- 路径规划面板中的"清除路径"按钮

## 测试建议

### 基础功能测试
1. 测试自动补全的响应速度和准确性
2. 验证选择建议后的坐标设置是否正确
3. 测试不同路径类型的规划结果
4. 验证途经点的添加、删除和编辑功能

### 高级功能测试
1. 测试多途经点的路径规划
2. 验证不同驾车策略的差异
3. 测试公交路径规划的时间设置
4. 验证路径历史记录功能

### 错误处理测试
1. 测试无效地址的处理
2. 验证网络错误时的降级处理
3. 测试自动补全不可用时的备用方案

## 常见问题

### 自动补全不显示
- 检查华为地图SDK是否正确加载
- 确认API密钥配置正确
- 查看浏览器控制台是否有错误信息

### 路径规划失败
- 确认起点和终点坐标是否有效
- 检查网络连接状态
- 验证路径规划服务是否可用

### 地图显示异常
- 刷新页面重新加载
- 检查地图容器尺寸设置
- 确认地图API密钥权限

## 开发调试

### 控制台日志
页面会在控制台输出详细的调试信息：
```javascript
console.log('自动补全选中的地点:', site, result)
console.log('当前起点:', origin.value)
console.log('路径规划结果:', route)
```

### 事件日志
页面底部的事件日志面板会记录所有重要操作：
- 自动补全选择事件
- 路径规划执行结果
- 错误和警告信息
- 用户操作记录

## 与其他测试页面的区别

### vs 高级功能测试页面
- **专注性**：专门针对路径规划功能
- **布局优化**：更大的地图显示区域
- **快速测试**：提供预设路线快速测试
- **详细日志**：专门的路径规划事件日志

### vs 自动补全测试页面
- **集成测试**：测试自动补全在实际场景中的应用
- **功能完整性**：包含完整的路径规划流程
- **用户体验**：更接近真实使用场景

## 总结

新的路径规划测试页面提供了一个专业的测试环境，让开发者和用户能够：

1. **专注测试**路径规划功能的各个方面
2. **快速验证**HWAutocomplete 在路径规划中的集成效果
3. **直观了解**不同路径类型和选项的差异
4. **方便调试**和排查问题

这个页面是华为地图 Vue3 组件库路径规划功能的完整展示，为用户提供了最佳的测试和学习体验。
