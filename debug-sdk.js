// 华为地图SDK调试脚本
// 在浏览器控制台中运行此脚本来调试SDK加载问题

console.log('=== 华为地图SDK调试开始 ===');

// 1. 检查环境变量
console.log('1. 环境变量检查:');
console.log('API Key:', import.meta?.env?.VITE_HUAWEI_MAP_API_KEY ? '已配置' : '未配置');
console.log('API URL:', import.meta?.env?.VITE_HUAWEI_MAP_API_URL || '使用默认');

// 2. 检查SDK加载状态
console.log('2. SDK加载状态:');
console.log('window.HWMapJsSDK:', !!window.HWMapJsSDK);
if (window.HWMapJsSDK) {
  console.log('SDK对象属性:', Object.keys(window.HWMapJsSDK));
}

// 3. 检查页面中的script标签
console.log('3. 页面script标签检查:');
const scripts = document.querySelectorAll('script[src*="mapapi.cloud.huawei.com"]');
console.log('华为地图script标签数量:', scripts.length);
scripts.forEach((script, index) => {
  console.log(`Script ${index + 1}:`, script.src);
  console.log(`加载状态:`, script.readyState || '未知');
});

// 4. 手动加载SDK测试
async function testSDKLoad() {
  console.log('4. 手动加载SDK测试:');
  try {
    // 动态导入map-loader
    const { loadHuaweiMapAPI, isHuaweiMapAPILoaded } = await import('/src/utils/map-loader.ts');
    
    console.log('当前SDK状态:', isHuaweiMapAPILoaded());
    
    if (!isHuaweiMapAPILoaded()) {
      console.log('开始加载SDK...');
      await loadHuaweiMapAPI();
      console.log('SDK加载完成:', isHuaweiMapAPILoaded());
    }
    
    // 测试SDK功能
    if (window.HWMapJsSDK) {
      console.log('5. SDK功能测试:');
      
      // 测试地图创建
      const testDiv = document.createElement('div');
      testDiv.style.width = '100px';
      testDiv.style.height = '100px';
      document.body.appendChild(testDiv);
      
      try {
        const map = new window.HWMapJsSDK.HWMap(testDiv, {
          center: { lat: 39.9042, lng: 116.4074 },
          zoom: 10
        });
        console.log('✅ 地图创建成功');
        
        // 测试路径规划服务
        const directionsService = new window.HWMapJsSDK.HWDirectionsService();
        console.log('✅ 路径规划服务创建成功');
        
        // 测试自动补全
        const input = document.createElement('input');
        document.body.appendChild(input);
        const autocomplete = new window.HWMapJsSDK.HWAutocomplete(input, {
          language: 'zh-CN'
        });
        console.log('✅ 自动补全创建成功');
        
        // 清理测试元素
        document.body.removeChild(testDiv);
        document.body.removeChild(input);
        
      } catch (error) {
        console.error('❌ SDK功能测试失败:', error);
      }
    }
    
  } catch (error) {
    console.error('❌ SDK加载测试失败:', error);
  }
}

// 5. 网络请求检查
function checkNetworkRequests() {
  console.log('6. 网络请求检查:');
  console.log('请打开浏览器开发者工具的Network标签页，查看是否有华为地图API的请求');
  console.log('正常的请求应该是: https://mapapi.cloud.huawei.com/mapjs/v1/api/js?callback=xxx&key=xxx');
}

// 6. 常见问题检查
function checkCommonIssues() {
  console.log('7. 常见问题检查:');
  
  // 检查CORS
  console.log('- CORS: 华为地图API应该支持跨域请求');
  
  // 检查API密钥格式
  const apiKey = import.meta?.env?.VITE_HUAWEI_MAP_API_KEY;
  if (apiKey) {
    console.log('- API密钥长度:', apiKey.length);
    console.log('- API密钥格式:', /^[A-Za-z0-9+/=]+$/.test(apiKey) ? '正常' : '可能有问题');
  }
  
  // 检查网络连接
  console.log('- 网络连接: 请确保能访问 https://mapapi.cloud.huawei.com');
}

// 执行所有检查
async function runAllChecks() {
  checkNetworkRequests();
  checkCommonIssues();
  await testSDKLoad();
  console.log('=== 华为地图SDK调试结束 ===');
}

// 导出函数供控制台使用
window.debugHuaweiMapSDK = {
  runAllChecks,
  testSDKLoad,
  checkNetworkRequests,
  checkCommonIssues
};

console.log('调试函数已准备就绪，运行 debugHuaweiMapSDK.runAllChecks() 开始完整检查');

// 自动运行检查
runAllChecks();
