# 开发文件
src/
public/
.vscode/
.idea/
*.log
*.lock

# 配置文件
vite.config.ts
vitest.config.ts
tsconfig*.json
eslint.config.ts
.env*
test.key*
*.key

# 测试文件
src/tests/
src/**/*.test.*
src/**/__tests__/
coverage/

# 示例和演示文件
src/examples/
index.html
src/App.vue
src/main.ts
src/assets/

# 构建工具
node_modules/
.pnpm-lock.yaml
pnpm-lock.yaml
package-lock.json
yarn.lock

# Git 相关
.git/
.gitignore
.github/

# 其他
*.tgz
.DS_Store
Thumbs.db
.nyc_output
.cache
temp/
tmp/

# 只保留构建产物和必要文件
# dist/ - 保留
# README.md - 保留
# LICENSE - 保留
# CHANGELOG.md - 保留
# package.json - 保留
