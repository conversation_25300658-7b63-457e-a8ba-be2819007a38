import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

const __dirname = fileURLToPath(new URL('.', import.meta.url))

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const isLib = mode === 'lib'

  return {
    plugins: [
      vue(),
      !isLib && vueDevTools(),
      isLib && dts({
        insertTypesEntry: true,
        copyDtsFiles: true,
        include: ['src/**/*'],
        exclude: ['src/**/*.test.*', 'src/tests/**/*', 'src/examples/**/*']
      })
    ].filter(Boolean),

    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      },
    },

    // 库模式配置
    ...(isLib && {
      build: {
        lib: {
          entry: resolve(__dirname, 'src/index.ts'),
          name: 'HuaweiMapVue3',
          fileName: 'huawei-map-vue3',
          formats: ['es', 'umd']
        },
        rollupOptions: {
          external: ['vue'],
          output: {
            globals: {
              vue: 'Vue'
            },
            assetFileNames: (assetInfo) => {
              if (assetInfo.name === 'style.css') return 'style.css'
              return assetInfo.name || ''
            }
          }
        },
        cssCodeSplit: false
      }
    })
  }
})
