{"name": "huawei-map-vue3", "version": "1.1.0", "description": "华为地图 Vue3 组件库 - 基于华为地图API开发的Vue3地图组件，提供完整的地图功能封装", "keywords": ["vue3", "huawei-map", "map", "component", "typescript", "vue-component", "地图组件", "华为地图"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/space-water-bear/hw-map.git"}, "homepage": "https://github.com/space-water-bear/hw-map#readme", "bugs": {"url": "https://github.com/space-water-bear/hw-map/issues"}, "type": "module", "main": "./dist/huawei-map-vue3.umd.cjs", "module": "./dist/huawei-map-vue3.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/huawei-map-vue3.js", "require": "./dist/huawei-map-vue3.umd.cjs"}, "./dist/style.css": "./dist/style.css"}, "files": ["dist", "README.md", "LICENSE", "CHANGELOG.md"], "scripts": {"dev": "vite", "dev:test": "vite --config vite.test.config.ts", "build": "run-p type-check \"build-lib {@}\" --", "build-lib": "vite build --mode lib", "build-demo": "vite build", "build-test": "vite build --config vite.test.config.ts", "preview": "vite preview", "preview:test": "vite preview --config vite.test.config.ts", "test:unit": "vitest", "test:components": "npm run dev:test", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "prepublishOnly": "npm run build", "release": "npm run build && npm publish"}, "peerDependencies": {"vue": "^3.0.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vitest/eslint-plugin": "^1.2.7", "@vue/eslint-config-typescript": "^14.5.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "npm-run-all2": "^8.0.4", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-dts": "^4.5.4", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue": "^3.5.17", "vue-tsc": "^2.2.10"}}